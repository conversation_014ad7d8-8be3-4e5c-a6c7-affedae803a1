import { useDispatch } from "react-redux";
import useStore from "./useStore";
import { set, isEqual, cloneDeep } from "lodash";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
/**
 * Hook dùng để lấy và cập nhật thiết lập cá nhân của người dùng từ server
 * Sử dụng React Query để quản lý state và cache
 */
const usePersonalSettings = () => {
  const authId = useStore("auth.auth.id", null);
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const {
    data: personalSettings,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["persist", "personalSettings", authId],
    queryFn: async () => {
      const res = await dispatch.thietLap.queryPersonalSetting();
      return res?.data || {};
    },
    structuralSharing: (oldData, newData) => {
      if (!oldData || !newData) return newData;

      const mergedData = { ...oldData };
      Object.keys(newData).forEach((key) => {
        if (!isEqual(oldData[key], newData[key])) {
          mergedData[key] = newData[key];
        }
      });

      return mergedData;
    },
    cacheTime: Infinity,
    onSuccess: () => {
      queryClient.setQueryDefaults(["persist", "personalSettings", authId], {
        staleTime: Infinity,
      });
    },
    enabled: !!authId,
  });

  const personalSettingsMutation = useMutation({
    mutationFn: ({ path, value }) => {
      const updatedData = set(cloneDeep(personalSettings), path, value);
      queryClient.setQueryData(
        ["persist", "personalSettings", authId],
        updatedData
      );
      return dispatch.thietLap.updatePersonalSetting(updatedData);
    },
    onSuccess: () => {
      refetch();
    },
  });

  return {
    personalSettings,
    personalSettingsMutation,
    isLoading,
    error,
  };
};

export default usePersonalSettings;
