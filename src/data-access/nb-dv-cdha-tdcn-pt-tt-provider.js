import { client, dataPath } from "client/request";
import { NB_DV_CLS_PT_TT } from "client/api";
import {
  combineUrlParams,
  checkValidBody,
  parseDataDsToArray,
} from "utils/index";
import apiBase from "./api-base";
import { message } from "antd";
import { t } from "i18next";

export default {
  ...apiBase.init({ API: NB_DV_CLS_PT_TT }),
  getTongHopDichVuCLS: (payload) => {
    const dsTrangThai = (payload.dsTrangThai || "") + "";
    const khongThucHien = ((window.location.pathname.includes(
      "/chan-doan-hinh-anh/chi-tiet-dich-vu"
    ) &&
      dsTrangThai &&
      ["25", "35", "43"].some((v) => dsTrangThai.includes(v))) ||
      (window.location.pathname.includes(
        "/chan-doan-hinh-anh/chi-tiet-dich-vu"
      ) &&
        dsTrangThai &&
        dsTrangThai == "15") ||
      window.location.pathname.includes("/chan-doan-hinh-anh/cho-tiep-don") ||
      (window.location.pathname.includes(
        "/chan-doan-hinh-anh/thuc-hien-cdha-tdcn"
      ) &&
        dsTrangThai &&
        ["25", "35", "43"].some((v) => dsTrangThai.includes(v)))) && {
      khongThucHien: false,
    };

    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_CLS_PT_TT}/tong-hop`, {
            ...payload,
            ...khongThucHien,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  putTongHopDichVuCLS: ({ page = 0, sort, size = 500, ...payload }) => {
    const path = window.location.pathname;
    const normalize = (arr) =>
      Array.isArray(arr)
        ? arr
          .flatMap((v) => String(v).split(","))
          .map((v) => Number(v.trim()))
          .filter((v) => !Number.isNaN(v))
        : arr;

    // ====== Xác định điều kiện khongThucHien ======
    const dsTrangThai = (payload.dsTrangThai || "") + "";
    const isCDHAChiTiet = path.includes("/chan-doan-hinh-anh/chi-tiet-dich-vu");
    const isChoTiepDon = path.includes("/chan-doan-hinh-anh/cho-tiep-don");
    const isThucHienCDHA = path.includes(
      "/chan-doan-hinh-anh/thuc-hien-cdha-tdcn"
    );
    const CHO_TIEP_NHAN = ["25", "35", "43"];
    const hasChoTiepNhan = CHO_TIEP_NHAN.some((v) => dsTrangThai.includes(v));

    const isKhongThucHien =
      (isCDHAChiTiet && (hasChoTiepNhan || dsTrangThai === "15")) ||
      isChoTiepDon ||
      (isThucHienCDHA && hasChoTiepNhan);

    const khongThucHien = isKhongThucHien ? { khongThucHien: false } : {};

    // ====== Xử lý body ======
    let body = checkValidBody(payload);
    body = parseDataDsToArray(body);

    body.dsTrangThai = normalize(body.dsTrangThai);
    body.dsKhoaId = normalize(body.dsKhoaId);

    if (body.thanhToan) {
      body.thanhToan = +body.thanhToan;
    }

    if (payload.dsTrangThaiHoan != null) {
      if (
        Array.isArray(payload.dsTrangThaiHoan) &&
        payload.dsTrangThaiHoan.length
      ) {
        body.dsTrangThaiHoan = payload.dsTrangThaiHoan;
      }
    }

    return new Promise((resolve, reject) => {
      client
        .put(
          combineUrlParams(`${dataPath}${NB_DV_CLS_PT_TT}/tong-hop`, {
            page: page + "",
            sort,
            size,
          }),
          {
            ...body,
            ...khongThucHien,
          }
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getNbDvCLSTongHop: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_CLS_PT_TT}/tong-hop/${id}`, {})
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  chiDinhCLS: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  tamTinhTienDVCLS: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/tinh-tien`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  onDeleteDichVu: ({ id, listDeletingId }) => {
    if (id)
      return new Promise((resolve, reject) => {
        client
          .delete(`${dataPath}${NB_DV_CLS_PT_TT}/${id}`)
          .then((s) => {
            if (s?.data?.code === 0) resolve(s?.data);
            else reject(s?.data);
          })
          .catch((e) => {
            reject(e);
          });
      });
    return new Promise((resolve, reject) => {
      client
        .delete(`${dataPath}${NB_DV_CLS_PT_TT}`, {
          data: listDeletingId,
        })
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getDsDichVuChiDinhCLS: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_CLS_PT_TT}/tong-hop`, payload)
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  themThongTinPhieu: (payload, id) => {
    return new Promise((resolve, reject) => {
      client
        .patch(
          `${dataPath}${NB_DV_CLS_PT_TT}/them-thong-tin/so-phieu/${id}`,
          payload
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  themThongTinDV: (payload, id) => {
    return new Promise((resolve, reject) => {
      client
        .patch(`${dataPath}${NB_DV_CLS_PT_TT}/them-thong-tin/${id}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getDanhSachBNCLS: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_CLS_PT_TT}/nguoi-benh`, payload)
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  phanPhong: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/phan-phong`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  tiepNhan: (phongThucHienId, payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          `${dataPath}${NB_DV_CLS_PT_TT}/tiep-nhan/${phongThucHienId}`,
          payload
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyTiepNhan: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/huy-tiep-nhan`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  coKetQua: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/co-ket-qua`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  chuyenHoiTinh: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/chuyen-hoi-tinh`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyChuyenHoiTinh: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/huy-chuyen-hoi-tinh`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyKetQua: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/huy-co-ket-qua`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  nhapKetQua: (id, payload) => {
    return new Promise((resolve, reject) => {
      client
        .patch(`${dataPath}${NB_DV_CLS_PT_TT}/ket-qua/${id}`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  khongThucHien: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/khong-thuc-hien/${id}`, {})
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  thucHien: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/thuc-hien/${id}`, {})
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getPhieuChiDinh: ({
    nbDotDieuTriId,
    soPhieuId,
    phieuChiDinhId,
    loai,
    ...payload
  }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_CLS_PT_TT}/phieu-chi-dinh`, {
            nbDotDieuTriId,
            soPhieuId,
            phieuChiDinhId,
            loai,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code == 0) resolve(s.data);
          reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  checkIn: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/check-in`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getDsNguoiBenhQms: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}${NB_DV_CLS_PT_TT}/qms`, payload))
        .then((s) => {
          if (s?.data?.code === 1007) {
            message.error(
              `${s?.data?.message}. ${t("qms.vuiLongThietLapPhong")}`
            );
          }
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  postNbTiepTheo: ({ phongThucHienId, nbTiepTheoId, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          `${dataPath}${NB_DV_CLS_PT_TT}/tiep-theo/${phongThucHienId}`,
          nbTiepTheoId ? { nbTiepTheoId, ...payload } : payload
        )
        .then((s) => {
          if (s?.data?.code == 0) resolve(s.data);
          reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  boQua: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/bo-qua`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  phieuKetQua: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DV_CLS_PT_TT}/phieu-ket-qua-cdha-tdcn/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getPhieuKetQua: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/phieu-ket-qua`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  inTemTuiHoSo: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/tem-tui-ho-so`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getDashboard: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/dashboard/theo-trang-thai`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getDashboardTheoNguonNb: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/dashboard/theo-nguon-nb`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getDashboardTheoThoiGian: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/dashboard/theo-thoi-gian`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getDashboardTheoBacSi: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/dashboard/theo-bac-si`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getDashboardTheoTrangThai: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .put(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/dashboard/pt-tt/theo-trang-thai`
          ),
          payload
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getDsPttt: (payload) => {
    const khongThucHienDvkt = window.location.pathname.includes(
      "/phau-thuat-thu-thuat/danh-sach-nguoi-benh"
    ) && { khongThucHienDvkt: false };
    // truyền thêm params không thực hiện khi ở màn hình này

    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_CLS_PT_TT}/pt-tt`, {
            ...payload,
            ...khongThucHienDvkt,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getChiTietPTTT: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/${id}`, {}))
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  updateThongTinPTTT: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/${payload.id}`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  doiDichVu: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .put(
          `${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/doi-dich-vu/${payload.id}`,
          payload
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getNguoiThucHien: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/nguoi-thuc-hien/${id}`,
            {}
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  saveNguoiThucHien: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .put(
          `${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/nguoi-thuc-hien/${payload.id}`,
          payload
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  doiDichVuChuaTT: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .patch(`${dataPath}${NB_DV_CLS_PT_TT}/them-thong-tin/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  giayChungNhanPTTT: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/giay-chung-nhan-pt-tt/${id}`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  giayChungNhanPTTTNoiTru: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/giay-chung-nhan-pt-tt`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  phieuPTTT: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/phieu-pt-tt/${id}`, {})
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  bangKeChiPhiVatTuTrongPT: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/bang-ke-vtyt/${id}`, {})
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  bangKeChiPhiThuocTrongPT: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/bang-ke-thuoc/${id}`, {})
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  phieuThanhToanPT: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          `${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/phieu-ghi-thanh-toan/${id}`,
          {}
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  themThongTin: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .patch(`${dataPath}${NB_DV_CLS_PT_TT}/them-thong-tin`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  tongHopSoLuong: () => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DV_CLS_PT_TT}/tong-hop-sl`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getBangKeChePhi: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/bang-ke-chi-phi/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getThoiGianTheoTrangThai: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/trang-thai/${id}`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  temSoKetNoi: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/tem-so-ket-noi`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getPhieuStt: (dsId) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_CLS_PT_TT}/phieu-stt`, { dsId })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getDsPtttCungCaKip: ({ id, nbDotDieuTriId }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/cung-ca/${id}`,
            {
              nbDotDieuTriId,
            }
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  xemKetQua: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/xem-ket-qua`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyXemKetQua: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/huy-xem-ket-qua`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getPhieuHenTraKQCls: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_CLS_PT_TT}/phieu-hen-ket-qua/${id}`
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  ngungYLenh: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/ngung-y-lenh`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyNgungYLenh: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/huy-ngung-y-lenh`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  khongThucHienNhieuDv: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_CLS_PT_TT}/khong-thuc-hien`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  searchAll: ({ page = "", sort, size = "", active = true, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_CLS_PT_TT}/tong-hop`, {
            page: page + "",
            sort,
            size,
            active,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
};
