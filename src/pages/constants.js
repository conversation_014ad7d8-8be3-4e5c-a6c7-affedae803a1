import React, { Suspense } from "react";
import { ROLES } from "constants/index";
import { Fallback, AuthWrapper } from "components";

export const Page =
  (Component, roles = []) =>
    (props) => {
      return (
        <Suspense fallback={<Fallback />}>
          <AuthWrapper accessRoles={roles} isCheckRoute>
            <Component {...props} />
          </AuthWrapper>
        </Suspense>
      );
    };

//trang chu v2

const TrangChuPage = React.lazy(() => import("./trangChu"));

//links powerbi
const LinksPowerbiPage = React.lazy(() => import("./home/<USER>/LinkPowerBi"));

// Application
const IconList = React.lazy(() => import("./application/IconList"));

//tiếp đón
const QuanLyTiepDon = React.lazy(() => import("./tiepDon"));
//tiem chung
const QuanLyTiemChung = React.lazy(() => import("./tiemChung"));

// kham benh
const KhamBenh = React.lazy(() => import("./khamBenh"));
const GoiNguoiBenhKhamBenh = React.lazy(() =>
  import("./khamBenh/GoiNguoiBenh")
);

// danh muc
const DanhMuc = React.lazy(() => import("./danhMuc"));

// Kios
const Kios = React.lazy(() => import("./kiosk"));
// Thu ngân
const ThuNgan = React.lazy(() => import("./thuNgan"));
const ManHinhPhuThuNgan = (props) => {
  return Page(
    React.lazy(() => import("./thuNgan/manHinhPhu")),
    []
  )(props);
};
const SSOLogout = (props) => {
  return Page(
    React.lazy(() => import("./sso/Logout")),
    []
  )(props);
};

// xet nghiem
const XetNghiem = React.lazy(() => import("./xetNghiem"));

// Quan ly thong bao
const QuanLyThongBao = React.lazy(() => import("./thongBao"));

// page
const ThietLapPage = React.lazy(() => import("./thietLap"));
const QuanTriPage = React.lazy(() => import("./quanTri"));
const TroGiup = React.lazy(() => import("./troGiup"));

//kho máu
const KhoMauPage = React.lazy(() => import("./khoMau"));

// kho
const KhoPage = React.lazy(() => import("./kho"));

const HoSoBenhAn = React.lazy(() => import("./hoSoBenhAn"));

// Nhà thuốc
const QuanLyNhaThuoc = React.lazy(() => import("./nhaThuoc"));

//Chan doan hinh anh
const ChanDoanHinhAnh = React.lazy(() => import("./chanDoanHinhAnh"));

//theo doi dieu tri
const TheoDoiDieuTri = React.lazy(() => import("./theoDoiDieuTri"));

//Quản lý dinh dưỡng
const QuanLyDinhDuong = React.lazy(() => import("./quanLyDinhDuong"));

// Pha chế thuốc
const PhaCheThuoc = React.lazy(() => import("./phaCheThuoc"));

//qms
const Qms = React.lazy(() => import("./qms"));

// Ký số
const KySo = React.lazy(() => import("./kySo"));

//dashboard
const Dashboard = React.lazy(() => import("./dashboard"));

// Bao cao
const BaoCaoPages = React.lazy(() => import("./baocao"));

const EditorConfig = React.lazy(() => import("./editor/config"));
const EditorReport = React.lazy(() => import("./editor/report"));
const EditorPreview = React.lazy(() => import("./editor/preview"));

// quyết toán bhyt
const quyetToanBHYT = React.lazy(() => import("./quyetToanBHYT"));
const QuanLyNoiTru = React.lazy(() => import("./quanLyNoiTru"));

const DieuTriDaiHan = React.lazy(() => import("./dieuTriDaiHan"));

const VitalSignsPrint = React.lazy(() =>
  import("components/VitalSigns/VitalSignsPrint")
);
const BangKePrint = React.lazy(() =>
  import("components/editor/pagePrint/BangKe")
);
// Kham Suc Khoe
const QuanLyKhamSucKhoe = React.lazy(() => import("./khamSucKhoe"));
// Goi dich vu
const QuanLyGoiDichVu = React.lazy(() => import("./goiDichVu"));

// quản lý phẫu thuật - thủ thuật
const PhauThuatThuThuat = React.lazy(() => import("./phauThuatThuThuat"));

// danh sách giấy đẩy cổng
const DanhSachGiayDayCong = React.lazy(() => import("./giayDayCong"));

// sinh hiệu
const QuanLySinhHieu = React.lazy(() => import("./sinhHieu"));

// phục hồi chức năng
const PhucHoiChucNangPage = React.lazy(() => import("./phucHoiChucNang"));

// Kế hoạch tổng hợp
const KeHoachTongHopPage = React.lazy(() => import("./keHoachTongHop"));

// Hội chẩn
const HoiChan = React.lazy(() => import("./hoiChan"));

const DvDieuDuong = React.lazy(() => import("./dvDieuDuong"));

// Login
const LoginPage = React.lazy(() => import("./login"));
// Login
const LockScreenPage = React.lazy(() => import("./lockScreen"));

const LienThongDonThuocDienTu = React.lazy(() =>
  import("./lienThongDonThuocDienTu")
);
const KiemSoatNhiemKhuan = React.lazy(() => import("./kiemSoatNhiemKhuan"));
const PhacDoDieuTri = React.lazy(() => import("./phacDoDieuTri"));

// danh sách đo thị lực
const DoThiLuc = React.lazy(() => import("./doThiLuc"));

// quản lý báo cáo ADR
const QuanLyBaoCaoAdr = React.lazy(() => import("./quanLyBaoCaoAdr"));

// từ điển y khoa
const TuDienYKhoa = React.lazy(() => import("pages/troGiup/TuDienYKhoa"));

// KPIs
const KPIs = React.lazy(() => import("./kpis"));

// quản lý điều trị lao
const QuanLyDieuTriLao = React.lazy(() => import("./quanLyDieuTriLao"));

// hẹn nội soi
const HenNoiSoi = React.lazy(() => import("./henNoiSoi"));

// quản lý nhân lực
const QuanLyNhanLuc = React.lazy(() => import("./quanLyNhanLuc"));
const QuanLyHoaHong = React.lazy(() => import("./hoaHong"));
const ChiTietHoaHongUnAuth = React.lazy(() =>
  import("./hoaHong/TinhTienHoaHong")
);
// quản lý yêu cầu
const QuanLyYeuCau = React.lazy(() => import("./quanLyYeuCau"));

const bangKePrint = (props) => {
  return Page(BangKePrint, [])(props);
};
const vitalSignsPrint = (props) => {
  return Page(VitalSignsPrint, [])(props);
};

const pages = {
  redirect: {
    component: Page(
      React.lazy(() => import("../app/Redirect")),
      []
    ),
    accessRoles: [],
    path: ["/redirect"],
    exact: true,
  },
  home: {
    component: Page(TrangChuPage, ["home_trangChu"]),
    accessRoles: [],
    path: ["/trang-chu", "/"],
    exact: true,
  },
  tuDienYKhoa: {
    component: Page(TuDienYKhoa, [ROLES["HE_THONG"].TU_DIEN_Y_KHOA]),
    accessRoles: [ROLES["HE_THONG"].TU_DIEN_Y_KHOA],
    path: ["/tro-giup/tu-dien-y-khoa"],
    exact: true,
  },
  iconList: {
    component: Page(IconList, []),
    accessRoles: [],
    path: ["/icon-list"],
    exact: true,
  },
  kiosk: {
    component: Page(Kios, []),
    accessRoles: [],
    path: "/kiosk",
    exact: false,
  },
  thuNgan: {
    component: Page(ThuNgan, []),
    accessRoles: [],
    path: "/thu-ngan",
  },
  xetNghiem: {
    component: Page(XetNghiem, []),
    accessRoles: [],
    path: "/xet-nghiem",
  },
  khamBenh: {
    component: Page(KhamBenh, [ROLES["KHAM_BENH"].XEM]),
    accessRoles: [],
    path: [
      "/kham-benh/:phongThucHienId/:maHoSo/:dichVu",
      "/kham-benh/:phongThucHienId",
      "/kham-benh",
    ],
  },
  goiNguoiBenhKhamBenh: {
    component: Page(GoiNguoiBenhKhamBenh, []),
    accessRoles: [],
    path: ["/:loaiQMS/goi-nguoi-benh/:phongThucHienId"],
  },
  thongBao: {
    component: Page(QuanLyThongBao, []),
    accessRoles: [],
    path: "/quan-ly-thong-bao",
    exact: true,
  },
  kho: {
    component: Page(KhoPage, []),
    accessRoles: [],
    path: "/kho",
    exact: false,
  },
  khoMau: {
    component: Page(KhoMauPage, [ROLES["HE_THONG"].KHO_MAU]),
    accessRoles: [],
    path: "/kho-mau",
    exact: false,
  },
  thietLap: {
    component: Page(ThietLapPage, []),
    accessRoles: [],
    path: "/thiet-lap",
    exact: false,
  },
  quanTri: {
    component: Page(QuanTriPage, [ROLES["HE_THONG"].QUAN_TRI_HE_THONG]),
    accessRoles: [],
    path: "/quan-tri",
    exact: false,
  },
  chanDoanHinhAnh: {
    component: Page(ChanDoanHinhAnh, []),
    accessRoles: [],
    path: "/chan-doan-hinh-anh",
    exact: false,
  },
  kySo: {
    component: Page(KySo, []),
    accessRoles: [],
    path: "/ky-so",
  },
  hoSoBenhAn: {
    component: Page(HoSoBenhAn, []),
    accessRoles: [],
    path: "/ho-so-benh-an",
    exact: false,
  },
  theoDoiDieuTri: {
    component: Page(TheoDoiDieuTri, []),
    accessRoles: [],
    path: "/theo-doi-nguoi-benh",
  },
  quanLyDinhDuong: {
    component: Page(QuanLyDinhDuong, [ROLES["HE_THONG"].QUAN_LY_DINH_DUONG]),
    accessRoles: [ROLES["HE_THONG"].QUAN_LY_DINH_DUONG],
    path: "/quan-ly-dinh-duong",
  },
  phaCheThuoc: {
    component: Page(PhaCheThuoc, [ROLES["PHA_CHE_THUOC"].PHA_CHE_THUOC]),
    accessRoles: [ROLES["PHA_CHE_THUOC"].PHA_CHE_THUOC],
    path: "/pha-che-thuoc",
  },
  qms: {
    component: Page(Qms, []),
    accessRoles: [],
    path: "/qms",
    exact: false,
  },
  baoCao: {
    component: Page(BaoCaoPages, []),
    accessRoles: [],
    path: ["/bao-cao"],
  },
  dashboard: {
    component: Page(Dashboard, []),
    accessRoles: [],
    path: ["/dashboard"],
  },
  danhMuc: {
    component: Page(DanhMuc, []),
    accessRoles: [],
    path: "/danh-muc",
    exact: false,
  },
  quyetToanBHYT: {
    component: Page(quyetToanBHYT, []),
    accessRoles: [],
    path: "/quyet-toan-bhyt",
  },
  troGiup: {
    component: Page(TroGiup, []),
    accessRoles: [],
    path: "/tro-giup",
  },
  tiepDon: {
    component: Page(QuanLyTiepDon, []),
    accessRoles: [],
    path: ["/quan-ly-tiep-don", "/tiep-don"],
  },
  tiemChung: {
    component: Page(QuanLyTiemChung, [
      ROLES["QUAN_LY_TIEM_CHUNG"].QUAN_LY_TIEM_CHUNG,
    ]),
    accessRoles: [ROLES["QUAN_LY_TIEM_CHUNG"].QUAN_LY_TIEM_CHUNG],
    path: ["/quan-ly-tiem-chung"],
  },
  quanLyNoiTru: {
    component: Page(QuanLyNoiTru, [ROLES["HE_THONG"].QUAN_LY_NOI_TRU]),
    accessRoles: [],
    path: "/quan-ly-noi-tru",
  },
  khamSucKhoe: {
    component: Page(QuanLyKhamSucKhoe, [ROLES["HE_THONG"].KHAM_SUC_KHOE]),
    accessRoles: [],
    path: ["/kham-suc-khoe"],
  },
  giayDayCong: {
    component: Page(DanhSachGiayDayCong, [ROLES["HE_THONG"].GIAY_DAY_CONG]),
    accessRoles: [],
    path: ["/danh-sach-giay-day-cong", "/giay-day-cong"],
  },
  sinhHieu: {
    component: Page(QuanLySinhHieu, []),
    accessRoles: [],
    path: ["/quan-ly-sinh-hieu", "/sinh-hieu"],
  },
  editorConfig: {
    component: Page(EditorConfig, [ROLES["EDITOR"].CONFIG_PHIEU]),
    accessRoles: [],
    path: "/editor/config/:formId",
  },
  editorReport: {
    component: Page(EditorReport, []),
    accessRoles: [],
    path: ["/editor/bao-cao/:maBaoCao/:id", "/editor/bao-cao/:maBaoCao"],
  },
  editorPreview: {
    component: Page(EditorPreview, []),
    accessRoles: [],
    path: "/editor/preview/:baoCaoId",
  },
  nhaThuoc: {
    component: Page(QuanLyNhaThuoc, []),
    accessRoles: [],
    path: ["/quan-ly-nha-thuoc", "/nha-thuoc"],
  },
  goiDichVu: {
    component: Page(QuanLyGoiDichVu, [ROLES["HE_THONG"].GOI_DICH_VU]),
    accessRoles: [],
    path: ["/goi-dich-vu"],
  },
  phauThuatThuThuat: {
    component: Page(PhauThuatThuThuat, [
      ROLES["HE_THONG"].PHAU_THUAT_THU_THUAT,
    ]),
    accessRoles: [ROLES["HE_THONG"].PHAU_THUAT_THU_THUAT],
    path: ["/phau-thuat-thu-thuat"],
  },
  createTemplate: {
    component: Page(EditorPreview, []),
    accessRoles: [],
    path: "/editor/template/:baoCaoId",
  },
  phucHoiChucNang: {
    component: Page(PhucHoiChucNangPage, [
      ROLES["HE_THONG"].PHUC_HOI_CHUC_NANG,
    ]),
    accessRoles: [],
    path: "/phuc-hoi-chuc-nang",
    exact: false,
  },
  keHoachTongHop: {
    component: Page(KeHoachTongHopPage, [ROLES["HE_THONG"].KE_HOACH_TONG_HOP]),
    accessRoles: [ROLES["HE_THONG"].KE_HOACH_TONG_HOP],
    path: "/ke-hoach-tong-hop",
  },
  dieuTriDaiHan: {
    component: Page(DieuTriDaiHan, []),
    accessRoles: [],
    path: "/dieu-tri-dai-han",
  },
  hoiChan: {
    component: Page(HoiChan, []),
    accessRoles: [ROLES["HOI_CHAN"].DANH_SACH_HOI_CHAN],
    path: "/hoi-chan",
  },
  lienThongDonThuocDienTu: {
    component: Page(LienThongDonThuocDienTu, [
      ROLES["LIEN_THONG_DON_THUOC_DIEN_TU"].XEM_DS_LIEN_THONG,
    ]),
    accessRoles: [],
    path: "/lien-thong-don-thuoc-dien-tu",
  },
  kiemSoatNhiemKhuan: {
    component: Page(KiemSoatNhiemKhuan, []),
    accessRoles: [],
    path: "/kiem-soat-nhiem-khuan",
  },
  linksPowerbiPage: {
    component: Page(LinksPowerbiPage, []),
    accessRoles: [],
    path: "/link-powerbi-page/:id",
  },
  phacDoDieuTri: {
    component: Page(PhacDoDieuTri, []),
    accessRoles: [],
    path: "/phac-do-dieu-tri",
  },
  dvDieuDuong: {
    component: Page(DvDieuDuong, []),
    accessRoles: [],
    path: "/kiem-tra-trang-thai-dv",
  },
  doThiLuc: {
    component: Page(DoThiLuc, [ROLES["DO_THI_LUC"].DANH_SACH_DO_THI_LUC]),
    accessRoles: [],
    path: "/do-thi-luc",
  },
  quanLyBaoCaoAdr: {
    component: Page(QuanLyBaoCaoAdr, []),
    accessRoles: [],
    path: "/quan-ly-bao-cao-adr",
  },
  kpis: {
    component: Page(KPIs, [ROLES["KPIS"].KPIS]),
    accessRoles: [ROLES["KPIS"].KPIS],
    path: "/kpis",
  },
  quanLyDieuTriLao: {
    component: Page(QuanLyDieuTriLao, []),
    accessRoles: [],
    path: "/quan-ly-dieu-tri-lao",
  },
  henNoiSoi: {
    component: Page(HenNoiSoi, [ROLES["HEN_NOI_SOI"].MODULE]),
    accessRoles: [ROLES["HEN_NOI_SOI"].MODULE],
    path: "/hen-noi-soi",
  },
  quanLyNhanLuc: {
    component: Page(QuanLyNhanLuc, []),
    accessRoles: [],
    path: "/quan-ly-nhan-luc",
    accessRoles: [ROLES["QUAN_LY_NHAN_LUC"].QUAN_LY_NHAN_LUC],
  },
  hoaHong: {
    component: Page(QuanLyHoaHong, [ROLES["COM"].MODULE]),
    accessRoles: [],
    accessRoles: [ROLES["COM"].MODULE],
    path: ["/quan-ly-hoa-hong"],
  },
  chiTietHoaHong: {
    component: Page(ChiTietHoaHongUnAuth, []),
    accessRoles: [],
    accessRoles: [],
    path: ["/hoa-hong"],
  },
  quanLyYeuCau: {
    component: Page(QuanLyYeuCau, [
      ROLES["QUAN_LY_YEU_CAU"].XEM_DS_QUAN_LY_YEU_CAU,
    ]),
    accessRoles: [],
    path: "/quan-ly-yeu-cau",
    accessRoles: [],
  },
};

export {
  ManHinhPhuThuNgan,
  bangKePrint,
  vitalSignsPrint,
  pages,
  LoginPage,
  LockScreenPage,
  SSOLogout,
};
