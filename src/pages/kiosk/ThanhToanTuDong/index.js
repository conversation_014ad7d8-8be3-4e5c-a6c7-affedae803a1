import React, { useState, useCallback, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Input, Spin } from "antd";

import { toSafePromise } from "lib-utils";
import { useFillMaHoSo } from "hooks";

import { isArray, isObject } from "utils/index";
import { SVG } from "assets";
import { Card } from "components";
import goiSoProvider from "data-access/goi-so-provider";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import { KiosWrapper } from "../components";
import { MainWrapper } from "./styled";
import { DOI_TUONG } from "constants/index";
import arrowImg from "assets/images/kiosk/arrow.png";
import printProvider from "data-access/print-provider";
import nbPhieuThuProvider from "data-access/nb-phieu-thu-provider";
import nbThanhToanProvider from "data-access/nb-thanh-toan-provider";

const RESET_TIMEOUT = 5000;
const QR_PATTERNS = {
  NB_CODE: /^NB.*\$$/,
};

const ERROR_MESSAGES = {
  PATIENT_NOT_FOUND: "kiosk.khongTimThayThongTinNguoiBenh",
  INSUFFICIENT_FUNDS:
    "kiosk.nguoiBenhConThieuTamUngVuiLongQuetMaQrTrenPhieuDeNapTienVaTiepTucThanhToan",
  GENERAL_ERROR: "kiosk.coLoiXayRaVuiLongThuLai",
};

const KioskThanhToanTuDong = React.memo(() => {
  const { t } = useTranslation();
  const [input, setInput] = useState("");
  const [state, _setState] = useState({
    error: "",
    loading: false,
    success: false,
    doiTuong: null,
    stt: null,
  });
  const { formatMaHoSo, testMaHoSo } = useFillMaHoSo();

  const timeoutRef = useRef(null);
  const inputRef = useRef(null);

  const setState = useCallback((data) => {
    _setState((prev) => ({
      ...prev,
      ...data,
    }));
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!state.loading && !state.success && !state.error && inputRef.current) {
      inputRef.current.focus();
    }
  }, [state.loading, state.success, state.error]);

  const clearResetTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const scheduleReset = useCallback(() => {
    clearResetTimeout();
    timeoutRef.current = setTimeout(() => {
      setState({ error: null, success: false, stt: null });
    }, RESET_TIMEOUT);
  }, [clearResetTimeout, setState]);

  const parseQRCode = useCallback((value) => {
    if (!value) return null;

    const array = value.split("|");

    if (testMaHoSo(value)) {
      return { maHoSo: formatMaHoSo(value) };
    } else if (QR_PATTERNS.NB_CODE.test(value)) {
      return { maNb: array[4] };
    } else if (value.endsWith("}")) {
      return { maNb: JSON.parse(value)?.maNb };
    } else if (array.length === 7 || array.length === 11) {
      return { maSoGiayToTuyThan: array[0] };
    } else {
      return { maSoGiayToTuyThan: array[0] };
    }
  }, []);

  const handleInsurancePrinting = useCallback(
    async (nbDotDieuTriId, dsPhieuThuId) => {
      try {
        if (isArray(dsPhieuThuId, true)) {
          for (const phieuThuId of dsPhieuThuId) {
            const [, receiptData] = await toSafePromise(
              nbPhieuThuProvider.getInPhieuThu({ phieuThuId })
            );
            if (receiptData) {
              await printProvider.printPdf({
                ...receiptData,
                silentPrint: true,
              });
            }
          }
        }

        if (nbDotDieuTriId) {
          const [, prescriptionData] = await toSafePromise(
            goiSoProvider.inPhieuSTTPhatThuoc({ nbDotDieuTriId })
          );
          if (prescriptionData) {
            await printProvider.printPdf({
              ...prescriptionData,
              silentPrint: true,
            });
          }
        }
      } catch (error) {
        console.error("HandleInsurancePrinting error:", error);
      }
    },
    []
  );

  const handleNonInsurancePrinting = useCallback(async (dsPhieuThuId) => {
    if (!isArray(dsPhieuThuId, true)) return;

    try {
      for (const phieuThuId of dsPhieuThuId) {
        const [, receiptData] = await toSafePromise(
          nbPhieuThuProvider.getInPhieuThu({ phieuThuId })
        );
        if (receiptData) {
          await printProvider.printPdf({
            ...receiptData,
            silentPrint: true,
          });
        }
      }
    } catch (error) {
      console.error("HandleNonInsurancePrinting error:", error);
    }
  }, []);

  const handlePrintQr = useCallback(async (thanhToanId) => {
    try {
      const [, thanhToanQrData] = await toSafePromise(
        nbThanhToanProvider.inPhieuHuongDanThanhToanQr({
          thanhToanId,
        })
      );
      if (thanhToanQrData) {
        await printProvider.printPdf({
          ...thanhToanQrData,
          silentPrint: true,
        });
      }
    } catch (error) {
      console.error("HandlePrintQr error:", error);
    }
  }, []);

  const processPayment = useCallback(
    async (params) => {
      setState({ loading: true, error: null });

      try {
        const [searchError, searchResult] = await toSafePromise(
          nbDotDieuTriProvider.searchNBDotDieuTriTongHop({
            ...params,
            dsDoiTuongKcb: 1,
            page: 0,
            size: 1,
          })
        );

        if (searchError || !isArray(searchResult?.data, true)) {
          setState({
            loading: false,
            error: t(ERROR_MESSAGES.PATIENT_NOT_FOUND),
          });
          scheduleReset();
          return;
        }

        const { id: nbDotDieuTriId, maHoSo, doiTuong } = searchResult.data[0];

        const [paymentError, paymentResult] = await toSafePromise(
          goiSoProvider.thanhToanNbTuDong({ maHoSo })
        );

        if (paymentError) {
          setState({
            loading: false,
            error: t(ERROR_MESSAGES.GENERAL_ERROR),
          });
          scheduleReset();
          return;
        }

        const isPaymentFailed =
          paymentResult?.code !== 0 || paymentResult?.data?.qrTamUng?.qr;

        if (isPaymentFailed) {
          const errorMessage =
            paymentResult?.code === 1
              ? paymentResult.message
              : t(ERROR_MESSAGES.INSUFFICIENT_FUNDS);

          if (paymentResult?.data?.qrTamUng?.id) {
            handlePrintQr(paymentResult.data.qrTamUng.id);
          }
          setState({ loading: false, error: errorMessage });
          scheduleReset();
          return;
        }

        setState({
          loading: false,
          success: true,
          doiTuong,
          stt: paymentResult?.data?.sttPhieuXuatKho,
        });

        if (doiTuong === DOI_TUONG.BAO_HIEM) {
          if (nbDotDieuTriId && paymentResult?.data?.dsPhieuThuId) {
            handleInsurancePrinting(
              nbDotDieuTriId,
              paymentResult.data.dsPhieuThuId
            );
          }
        } else if (paymentResult?.data?.dsPhieuThuId) {
          handleNonInsurancePrinting(paymentResult?.data?.dsPhieuThuId);
        }

        scheduleReset();
      } catch (error) {
        console.error("Payment processing error:", error);
        setState({
          loading: false,
          error: t(ERROR_MESSAGES.GENERAL_ERROR),
        });
        scheduleReset();
      }
    },
    [
      setState,
      scheduleReset,
      handleInsurancePrinting,
      handleNonInsurancePrinting,
      handlePrintQr,
      t,
    ]
  );

  const onChange = useCallback((e) => {
    setInput(e.target.value.trim());
  }, []);

  const onKeyDown = useCallback(
    async (e) => {
      const value = e.target.value || input;

      if (e.key === "Enter" || e.key === "Tab") {
        if (!value || state.loading) return;

        const params = parseQRCode(value);
        if (params && isObject(params, true)) {
          setInput("");
          await processPayment(params);
        }
      }
    },
    [input, state.loading, parseQRCode, processPayment]
  );

  const baseContent = () => {
    return (
      <>
        <h1 id="main-title">{t("qms.quetMaQr")}</h1>
        <p id="instruction-text">
          {t("kiosk.vuiLongQuetMaQrCodeTrenCccdAppVneidHoacMaQrCodeDaDuocCap")}
        </p>
        <div className="icon-wrapper">
          <SVG.IcCCCD />
          <SVG.IcVneid />
          <SVG.IcPhieuQr />
        </div>
        <div className="input-wrapper">
          <div className="input-search">
            <Input
              ref={inputRef}
              placeholder={t("kiosk.qrCccdQrMaNb")}
              onKeyDown={onKeyDown}
              onChange={onChange}
              value={input}
              disabled={state.loading}
              autoComplete="off"
              autoFocus={!state.loading}
              aria-label={t("kiosk.qrCccdQrMaNb")}
              aria-describedby="instruction-text"
              aria-invalid={!!state.error}
              suffix={state.loading ? <Spin size="small" /> : <SVG.IcQrCode />}
            />
          </div>
          {state.loading && (
            <div
              className="loading-text"
              role="status"
              aria-live="polite"
              aria-label={t("kiosk.dangXuLyVuiLongCho")}
            >
              {t("kiosk.dangXuLyVuiLongCho")}
            </div>
          )}
        </div>
      </>
    );
  };

  const formattedNumber = useCallback((num = 1) => {
    return String(num).padStart(4, "0");
  }, []);

  const successContent = () => {
    if (state.doiTuong === DOI_TUONG.KHONG_BAO_HIEM) {
      return (
        <div className="success-content" role="alert" aria-live="polite">
          <SVG.IcCheckCircleGreen aria-hidden="true" />
          <h1 id="success-title">{t("kiosk.thanhToanTuDongThanhCong")}</h1>
        </div>
      );
    } else {
      return (
        <div className="success-content" role="alert" aria-live="polite">
          <SVG.IcCheckCircleGreen aria-hidden="true" />
          <h1 id="success-title">
            {t("kiosk.thanhToanTuDongThanhCongVuiLongNhanSoThuTuLayThuocBHYT")}
          </h1>
          <div
            className="stt-content"
            role="region"
            aria-labelledby="queue-number"
          >
            <div className="stt" id="queue-number">
              {t("kiosk.soThuTuCuaBanLa")}:
            </div>
            <div
              className="number"
              aria-label={`${t("kiosk.soThuTuCuaBanLa")}: ${formattedNumber(
                state.stt ?? 0
              )}`}
            >
              {formattedNumber(state.stt ?? 0)}
            </div>
          </div>
          <div className="footer">
            <div className="footer-text">
              {t("kiosk.xinMoiLayPhieuDaDuocNhaRa")}
            </div>
            <div className="image">
              <img
                src={arrowImg}
                alt={t("kiosk.huongDanLayPhieu")}
                aria-hidden="true"
              />
            </div>
          </div>
        </div>
      );
    }
  };

  const errorContent = () => {
    return (
      <div className="error-content" role="alert" aria-live="assertive">
        <SVG.IcCloseCircle
          color={"var(--color-red-primary)"}
          aria-hidden="true"
        />
        <h1
          id="error-title"
          dangerouslySetInnerHTML={{
            __html: t("kiosk.thanhToanTuDongKhongThanhCongMessage", {
              message: state.error,
            }),
          }}
        />
      </div>
    );
  };

  return (
    <KiosWrapper>
      <MainWrapper>
        {!state.success && !state.error && (
          <header className="top">
            <div className="header">
              <h1 className="title">{t("kiosk.kioskThanhToanTuDong")}</h1>
              <p className="sub-header">{t("kiosk.xinKinhChaoQuyKhach")}</p>
            </div>
          </header>
        )}
        <main className={"content"} role="main">
          <div className="header-content" role="banner">
            <SVG.IcThuTamUng aria-hidden="true" />
            <span>{t("kiosk.thanhToanTuDong").toUpperCase()}</span>
          </div>
          <Card
            className="card-content"
            role="region"
            aria-labelledby={
              state.success
                ? "success-title"
                : state.error
                ? "error-title"
                : "main-title"
            }
          >
            {state.success
              ? successContent()
              : state.error
              ? errorContent()
              : baseContent()}
          </Card>
        </main>
      </MainWrapper>
    </KiosWrapper>
  );
});

KioskThanhToanTuDong.displayName = "KioskThanhToanTuDong";

export default KioskThanhToanTuDong;
