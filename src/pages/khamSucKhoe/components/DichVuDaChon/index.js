import React, { useMemo, useState } from "react";
import { MainChoose } from "./styled";
import { CheckCircleOutlined } from "@ant-design/icons";
import { formatDecimal } from "utils";
import {
  TableWrapper,
  HeaderSearch,
  InputTimeout,
  Select,
  Checkbox,
} from "components";
import { InputNumberFormat } from "components/common";
import { useTranslation } from "react-i18next";
import { useListAll } from "hooks";
import { LOAI_DICH_VU } from "constants/index";

const DichVuDaChon = ({
  data = [],
  onDeleteItem,
  onChangeChooseItem,
  loaiDichVu,
}) => {
  const { t } = useTranslation();
  const [listAllBenhPham] = useListAll("benhPham", {}, true);
  const [state, _setState] = useState({
    searchBenhPhamIds: {},
  });
  const setState = (data) => {
    _setState((prev) => {
      return { ...prev, ...data };
    });
  };

  const listChooseDVMemo = useMemo(() => {
    return data.map((item, idx) => ({
      ...item,
      stt: idx + 1,
    }));
  }, [data]);

  const totalPriceMemo = useMemo(() => {
    const sum = data.reduce((s, { giaKhongBaoHiem }) => s + giaKhongBaoHiem, 0);

    return sum;
  }, [data]);

  const deleteDichVu = (item) => (e) => {
    e.preventDefault();

    onDeleteItem(item.uniqueKey);
  };

  const columns = [
    {
      title: <HeaderSearch isTitleCenter={true} title={<Checkbox checked />} />,
      key: "stt",
      width: 40,
      align: "center",
      render: (item, list, index) => (
        <Checkbox checked onChange={deleteDichVu(list)} />
      ),
    },
    {
      title: <HeaderSearch title={t("common.stt")} isTitleCenter={true} />,
      dataIndex: "stt",
      width: 50,
      align: "center",
    },
    {
      title: <HeaderSearch title={t("common.tenDichVu")} />,
      width: 250,
      dataIndex: "ten",
    },
    {
      title: <HeaderSearch title={t("khamSucKhoe.soLuong")} />,
      width: 70,
      dataIndex: "soLuong",
      render: (item, list, index) => {
        return (
          <div onClick={(event) => event.stopPropagation()}>
            <InputTimeout
              type="number"
              value={item || 1}
              style={{ width: 50 }}
              min={1}
              step={1}
              onChange={onChangeChooseItem("soLuong", list)}
            />
          </div>
        );
      },
    },
    {
      title: <HeaderSearch title={t("khamSucKhoe.donGia")} />,
      width: 120,
      dataIndex: "giaKhongBaoHiem",
      render: (item, list, index) => {
        return (
          <InputNumberFormat
            value={item || 0}
            placeholder={t("khamSucKhoe.nhapDonGia")}
            onChange={onChangeChooseItem("giaKhongBaoHiem", list)}
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("khamSucKhoe.phongThucHien")} />,
      width: 180,
      dataIndex: "phongId",
      render: (item, list, index) => {
        const _dataPhong = list?.dsPhong || list?.dsPhongThucHien || [];

        return (
          <Select
            onChange={onChangeChooseItem("phongId", list)}
            value={item}
            placeholder={t("khamSucKhoe.chonPhongThucHien")}
            data={_dataPhong.map((x) => ({
              ...x,
              id: x.phongId,
            }))}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.chiDinh.loaiHinhThanhToan")}
          isTitleCenter={true}
        />
      ),
      dataIndex: "loaiHinhThanhToanId",
      key: "loaiHinhThanhToanId",
      width: 160,
      show: true,
      i18Name: "khamBenh.chiDinh.loaiHinhThanhToan",
      render: (item, list, idx) => {
        const dataSource = (list.dsLoaiHinhThanhToan || [])
          .map((item) => ({
            id: item.loaiHinhThanhToanId,
            ten: item.tenLoaiHinhThanhToan,
            uuTien: item.uuTien,
          }))
          .sort((a, b) => b.uuTien - a.uuTien);

        return (
          <Select
            value={item}
            data={dataSource}
            onChange={onChangeChooseItem("loaiHinhThanhToanId", list)}
            style={{ margin: "0px" }}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch title={t("xetNghiem.benhPham")} isTitleCenter={true} />
      ),
      dataIndex: "benhPhamId",
      key: "benhPhamId",
      width: 200,
      align: "center",
      i18Name: "xetNghiem.benhPham",
      hidden:
        loaiDichVu &&
        ![LOAI_DICH_VU.XET_NGHIEM, LOAI_DICH_VU.BO_CHI_DINH].includes(
          loaiDichVu
        ),
      render: (item, list, index) => {
        const modifyList = !!list?.dsBenhPhamId?.length
          ? listAllBenhPham?.filter((i) => list?.dsBenhPhamId?.includes(i.id))
          : listAllBenhPham;
        return (
          <Select
            value={item}
            onSearch={(e) => {
              setState({ searchBenhPhamIds: { [list.id]: !!e } });
            }}
            defaultValue={
              list?.dsBenhPhamId?.length === 1 ? list?.dsBenhPhamId?.[0] : null
            }
            data={
              state?.searchBenhPhamIds?.[list.id] ? listAllBenhPham : modifyList
            }
            placeholder={t("khamBenh.ketQua.chonBenhPham")}
            onChange={onChangeChooseItem("benhPhamId", list)}
            dropdownMatchSelectWidth={300}
          />
        );
      },
    },
  ];

  return (
    <MainChoose>
      <div className="choose-header">
        <div>
          <CheckCircleOutlined /> {t("khamSucKhoe.daChon")}
        </div>
        <div>
          {t("khamSucKhoe.tongTien")}: {formatDecimal(totalPriceMemo)}
          {t("khamSucKhoe.vnd")}
        </div>
      </div>

      <div className="choose-content">
        <TableWrapper
          rowKey={(record, index) => record.uniqueKey}
          columns={columns}
          dataSource={listChooseDVMemo}
        />
      </div>
    </MainChoose>
  );
};

export default DichVuDaChon;
