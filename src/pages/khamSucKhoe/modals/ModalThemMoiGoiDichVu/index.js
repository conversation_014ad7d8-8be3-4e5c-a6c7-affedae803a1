import React, {
  forwardRef,
  useRef,
  useState,
  useImperativeHandle,
  useEffect,
  useMemo,
} from "react";
import { Main } from "./styled";
import { Input, Row, Col, List, message } from "antd";
import { useDispatch } from "react-redux";
import DichVuDaChon from "pages/khamSucKhoe/components/DichVuDaChon";
import { SearchOutlined } from "@ant-design/icons";
import { cloneDeep, groupBy } from "lodash";
import { formatDecimal, parseFloatNumber } from "utils";
import {
  Checkbox,
  Button,
  ModalTemplate,
  InputTimeout,
  Select,
  TableWrapper,
  Pagination,
} from "components";
import { ENUM, LOAI_DICH_VU } from "constants/index";
import { useTranslation } from "react-i18next";
import { useEnum, useLoading } from "hooks";
import { SVG } from "assets";
import { useSelector } from "react-redux";
import { isEmpty } from "lodash";
import { isArray } from "utils/index";

const getPhongThucHienId = (item, khoaId) => {
  const { dsPhongThucHien } = item || {};
  return isArray(dsPhongThucHien, true)
    ? dsPhongThucHien.find((i) => i.khoaChiDinhId && i.khoaChiDinhId === khoaId)
        ?.phongId ?? dsPhongThucHien[0]?.phongId
    : null;
};

const ModalThemMoiGoiDichVu = (props, ref) => {
  const refFuncSubmit = useRef(null);
  const refInputTenGoi = useRef(null);
  const { refreshList, goiDV, hopDongKskId } = props;
  const { t } = useTranslation();
  const refModal = useRef(null);
  const [listloaiDichVu] = useEnum(ENUM.LOAI_DICH_VU, []);
  const { showLoading, hideLoading } = useLoading();

  //state
  const [state, _setState] = useState({
    loaiDichVu: "",
    textSearchDv: "",
    defaultChooseDv: [],
    listDichVuTrongBo: [],
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const [formState, setFormState] = useState({
    ma: "",
    ten: "",
  });

  const [selectedGoiDV, setSelectedGoiDV] = useState(null);

  //redux
  const {
    listDVKSK,
    listChooseDV,
    ttGoi,
    validGoi,
    listGoiDVKSK,
    dsGoi,
    page,
    size,
    totalElements,
  } = useSelector((state) => state.dichVuKSK);

  const {
    auth: { auth = {} },
    dichVuKSK: {
      searchDvKSK,
      updateData,
      postGoiDichVu,
      validateTTGoi,
      deleteMultiDichVuChiTiet,
      updateGoiDichVu,
      insertMultipleDichVuChiTiet,
      updateDichVuChiTiet,
    },
    phongThucHien: { onSearchParams: getListPhongThucHien },
    chiDinhKhamBenh: { tamTinhTien },
  } = useDispatch();

  useImperativeHandle(ref, () => ({
    show: ({ khoaId, loaiDoiTuongId } = {}) => {
      setState({
        show: true,
        loaiDichVu: LOAI_DICH_VU.BO_CHI_DINH,
        textSearchDv: "",
        khoaId,
        loaiDoiTuongId,
      });

      updateData({
        ttGoi: {
          ma: "",
          ten: "",
        },
      });
    },
  }));

  //effect
  useEffect(() => {
    if (state.show) {
      onGetListService({
        loaiDichVu: state.loaiDichVu,
      });
    }
  }, [state.show]);

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(async () => {
    if (goiDV.id) {
      const groupGoi = groupBy(dsGoi, "boChiDinhId");
      if (!groupGoi[goiDV.id]) {
        updateData({
          listChooseDV: [],
        });
        setState({
          defaultChooseDv: [],
        });

        return;
      }

      const dsDvDaChonTrongGoi = await Promise.all(
        groupGoi[goiDV.id]
          .filter((x) => x.dichVuId)
          .map(async (item) => {
            try {
              const resPhongThucHien = await getListPhongThucHien({
                dsDichVuId: [item.dichVuId],
                doiTuongKcb: 1,
                khoaChiDinhId: state.khoaId,
                loaiDoiTuongId: state.loaiDoiTuongId,
              });

              return {
                ...item,
                dsPhong: resPhongThucHien,
              };
            } catch (error) {
              console.error(error);
              return item;
            }
          })
      );

      updateData({
        listChooseDV: dsDvDaChonTrongGoi.map((x) => ({
          ...x,
          ten: x.tenDichVu,
          uniqueKey: `${x.id || "dv"}-${x.dichVuId}`,
        })),
      });
      setState({
        defaultChooseDv: dsDvDaChonTrongGoi.map((x) => ({
          ...x,
          uniqueKey: `${x.id || "dv"}-${x.dichVuId}`,
        })),
      });

      setFormState({
        ...formState,
        ma: groupGoi[goiDV.id][0]?.maBoChiDinh,
        ten: groupGoi[goiDV.id][0]?.tenBoChiDinh,
      });

      updateData({
        ttGoi: {
          ma: groupGoi[goiDV.id][0]?.maBoChiDinh,
          ten: groupGoi[goiDV.id][0]?.tenBoChiDinh,
        },
      });
    }
  }, [goiDV, dsGoi]);

  //memo
  const dataloaiDichVu = useMemo(() => {
    return [
      ...listloaiDichVu.filter((allItem) =>
        [
          LOAI_DICH_VU.KHAM,
          LOAI_DICH_VU.XET_NGHIEM,
          LOAI_DICH_VU.CDHA,
          LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
          LOAI_DICH_VU.BO_CHI_DINH,
          LOAI_DICH_VU.NGOAI_DIEU_TRI,
        ].includes(allItem.id)
      ),
      {
        ten: "Tất cả loại phiếu chỉ định",
        id: "",
        i18n: "khamSucKhoe.tatCaLoaiPhieuChiDinh",
      },
    ];
  }, [listloaiDichVu, auth]);

  const listDVKSKMemo = useMemo(() => {
    const _listDVKSKFilter =
      state.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH
        ? state?.listDichVuTrongBo
        : listDVKSK;
    return _listDVKSKFilter.map((item) => {
      const donGia = `${formatDecimal(item._giaKhongBaoHiem)} | ${t(
        "khamSucKhoe.bh"
      )}: ${formatDecimal(item.giaBaoHiem)} | ${t(
        "khamSucKhoe.phuThu"
      )}: ${formatDecimal(item.giaPhuThu)}`;
      return {
        ...item,
        donGia,
        uniqueKey: `${item.id || "dv"}-${item.dichVuId}`,
      };
    });
  }, [listDVKSK, state?.listDichVuTrongBo, listChooseDV, state.loaiDichVu]);

  const onTamTinhTien = (listSelectedDv) => {
    const payload = listSelectedDv.map((item) => ({
      dsPhongThucHien: item.dsPhongThucHien,
      nbDichVu: {
        dichVu: {
          ten: item.ten,
          ma: item.ma,
        },
        dichVuId: item?.dichVuId,
        boChiDinhId: item.boChiDinhId || undefined,
        soLuong: item.soLuong || 1,
        loaiDichVu: item?.loaiDichVu,
        nbGoiDvId: item?.nbGoiDvId || undefined,
        nbGoiDvChiTietId: item?.nbGoiDvChiTietId || undefined,
        loaiHinhThanhToanId: item?.loaiHinhThanhToanId,
        tyLeTtDv:
          !state.isHiddenTyLett &&
          item?.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
          state.isPhauThuat
            ? item?.tyLeTtDv || 100
            : undefined,
        tuTra: item.tuTra,
        khongTinhTien: item.khongTinhTien,
        ghiChu: item.ghiChu,
        nguonKhacId: item.nguonKhacId,
        thoiGianThucHien: item.thoiGianThucHien,
      },
      boChiDinhId: item.boChiDinhId || undefined,
      nbDvKyThuat: {
        phongThucHienId: item.phongThucHienId,
        tuVanVienId: item.tuVanVienId || state.tuVanVienId,
        capCuu: item.capCuu,
      },
      benhPhamId:
        !item?.benhPhamId && item?.dsBenhPhamId?.length == 1
          ? item?.dsBenhPhamId?.[0]
          : item.benhPhamId,
      ...(item?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
        ? { phongLayMauId: item.phongLayMauId }
        : {}),
    }));

    tamTinhTien({
      dsDichVu: payload,
    }).then((s) => {
      listSelectedDv.forEach((item, index) => {
        item._giaKhongBaoHiem = item.giaKhongBaoHiem;
        if (Array.isArray(s)) {
          s.forEach((x) => {
            if (item.dichVuId === x.nbDichVu.dichVuId) {
              item.giaKhongBaoHiem = x.nbDichVu.giaKhongBaoHiem;
            }
          });
        }
      });
      setState({ listDichVuTrongBo: listSelectedDv });
    });
  };

  useEffect(() => {
    if (listDVKSK?.length && state.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH) {
      onTamTinhTien(listDVKSK);
    }
  }, [listDVKSK, state.loaiDichVu]);

  const isThemMoi = useMemo(() => {
    if (goiDV.id) return false;

    return true;
  }, [goiDV]);

  const listGoiDVMemo = useMemo(() => {
    const searchTxt = state.textSearchDv?.toLowerCase().unsignText() || "";

    return state.loaiDichVu !== LOAI_DICH_VU.BO_CHI_DINH
      ? listGoiDVKSK
      : listGoiDVKSK.filter(
          (option) =>
            option?.ten?.toLowerCase().unsignText().indexOf(searchTxt) >= 0
        );
  }, [listGoiDVKSK, state.textSearchDv, state.loaiDichVu]);

  //function
  const onGetListService = (payload = {}) => {
    searchDvKSK({
      ...payload,
      sort: "ma",
      dsDoiTuongSuDung: [40],
      page: payload.page ?? 0,
      size: payload.size ?? 20,
      doiTuongKcb: 1,
      timKiem: payload?.timKiem ?? state.textSearchDv ?? "",
      ...(state.khoaId &&
        payload?.loaiDichVu !== LOAI_DICH_VU.BO_CHI_DINH && {
          khoaChiDinhId: state.khoaId,
        }),
      ...(state.loaiDoiTuongId &&
        payload?.loaiDichVu !== LOAI_DICH_VU.BO_CHI_DINH && {
          loaiDoiTuongId: state.loaiDoiTuongId,
        }),
    });
  };

  const onSelectService = (item) => (e) => {
    const { checked } = e.target;
    let _listChooseDV = cloneDeep(listChooseDV);

    if (checked) {
      if (_listChooseDV.findIndex((x) => x.dichVuId == item.dichVuId) > -1) {
        message.error(
          `${t("khamSucKhoe.dichVu")} ${item.ten} ${t("khamSucKhoe.daTonTai")}.`
        );
      }

      const loaiHinhThanhToanId = (item?.dsLoaiHinhThanhToan?.sort(
        (a, b) => b.uuTien - a.uuTien
      ) || [])[0]?.loaiHinhThanhToanId;

      let giaKhongBaoHiem = item?.giaKhongBaoHiem;
      let giaBaoHiem = item?.giaBaoHiem;
      let loaiHinhThanhToan;

      if (item?.dsLoaiHinhThanhToan?.length && loaiHinhThanhToanId) {
        loaiHinhThanhToan = item?.dsLoaiHinhThanhToan.find(
          (x) => x.loaiHinhThanhToanId === loaiHinhThanhToanId
        );
        if (loaiHinhThanhToan) {
          giaBaoHiem = loaiHinhThanhToan.giaBaoHiem;
          giaKhongBaoHiem = loaiHinhThanhToan.giaKhongBaoHiem;
        }
      }

      _listChooseDV.push({
        ...item,
        soLuong: item.soLuong || 1,
        phongId: getPhongThucHienId(item, state.khoaId),
        loaiHinhThanhToanId,
        giaKhongBaoHiem,
        giaBaoHiem,
      });
    } else {
      _listChooseDV = _listChooseDV.filter(
        (x) => x.uniqueKey !== item.uniqueKey
      );
    }

    updateData({
      listChooseDV: _listChooseDV,
    });
  };

  const onChangeGroupService = (value) => {
    let addState = {};
    if (value !== LOAI_DICH_VU.BO_CHI_DINH) {
      addState.listDichVuTrongBo = [];
    }
    setState({ loaiDichVu: value, ...addState });

    onGetListService({
      ...(value
        ? { loaiDichVu: value }
        : {
            dsLoaiDichVu: [
              LOAI_DICH_VU.KHAM,
              LOAI_DICH_VU.XET_NGHIEM,
              LOAI_DICH_VU.CDHA,
              LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            ],
          }),
    });
  };

  const onSubmit = (e) => {
    e.preventDefault();
  };

  refFuncSubmit.current = onSubmit;

  const onClose = () => {
    updateData({
      listChooseDV: [],
      listDVKSK: [],
    });

    setSelectedGoiDV(null);
    setFormState({ ma: "", ten: "" });
    setState({ show: false, textSearchDv: "" });
  };

  function onRow(record, index) {
    return {
      onClick: (event) => {
        const _findIndex = listChooseDV.findIndex(
          (x) => x.uniqueKey === record.uniqueKey
        );

        const e = { target: { checked: _findIndex === -1 } };
        onSelectService(record)(e);
      },
    };
  }

  function handleChangeData(value, variables) {
    setFormState({ ...formState, [`${variables}`]: value });
  }

  function onBlur(value, variables) {
    updateData({
      ttGoi: {
        ...ttGoi,
        [`${variables}`]: value,
      },
    });
  }

  async function onSave() {
    // if (listChooseDV && listChooseDV.length === 0) {
    //   message.error(t("khamSucKhoe.vuiLongChonDichVuTrongGoi"));
    //   return;
    // }
    showLoading();
    validateTTGoi()
      .then(async () => {
        if (isThemMoi) {
          postGoiDichVu({
            hopDongKskId,
            donGiaTheoBo: true,
            ...ttGoi,
            ma: isEmpty(ttGoi?.ma) ? undefined : ttGoi.ma,
          }).then((data) => {
            if (listChooseDV && listChooseDV.length > 0) {
              insertDichVu(data.id);
            } else {
              onClose();
              refreshList({ trongGoi: true });
            }

            hideLoading();
          });
        } else {
          //sửa thông tin gói
          await updateGoiDichVu({
            id: goiDV.id,
            hopDongKskId,
            ...{
              ten: ttGoi.ten,
              ma: isEmpty(ttGoi?.ma) ? undefined : ttGoi.ma,
            },
          });

          //add những dịch vụ mới
          const _addDv = listChooseDV.filter(
            (x1) =>
              state.defaultChooseDv.findIndex(
                (x2) => x2.uniqueKey === x1.uniqueKey
              ) == -1
          );
          if (_addDv && _addDv.length > 0) {
            await insertMultipleDichVuChiTiet(
              _addDv.map((item) => {
                return {
                  boChiDinhId: goiDV.id,
                  dichVuId: item.dichVuId,
                  soLuong: item.soLuong || 1,
                  giaKhongBaoHiem: item.giaKhongBaoHiem,
                  phongId: item.phongId,
                  loaiHinhThanhToanId: item.loaiHinhThanhToanId,
                  benhPhamId:
                    !item?.benhPhamId && item?.dsBenhPhamId?.length == 1
                      ? item?.dsBenhPhamId?.[0]
                      : item.benhPhamId,
                };
              })
            );
          }

          //xóa những dịch vụ cũ
          const _deleteDv = state.defaultChooseDv.filter(
            (x1) =>
              listChooseDV.findIndex((x2) => x2.uniqueKey === x1.uniqueKey) ==
              -1
          );
          if (_deleteDv && _deleteDv.length > 0) {
            await deleteMultiDichVuChiTiet(_deleteDv.map((x) => x.id));
          }

          //update những dịch vụ có thay đổi
          //bổ sung thêm điều kiện dịch vụ thay đổi ko phải dv thêm mới
          const _updateDv = listChooseDV.filter(
            (x) =>
              x.isNeedUpdate &&
              _addDv.findIndex((x2) => x2.dichVuId === x.dichVuId) === -1
          );
          await Promise.all(
            (_updateDv || []).map(async (x) => {
              await updateDichVuChiTiet({
                id: x.id,
                soLuong: x.soLuong,
                phongId: x.phongId,
                loaiHinhThanhToanId: x.loaiHinhThanhToanId,
                giaKhongBaoHiem: x.giaKhongBaoHiem,
                benhPhamId:
                  !x?.benhPhamId && x?.dsBenhPhamId?.length == 1
                    ? x?.dsBenhPhamId?.[0]
                    : x.benhPhamId,
              });
            })
          );

          //refresh list dịch vụ
          onClose();
          refreshList({ trongGoi: true });
          hideLoading();
        }
      })
      .finally(() => hideLoading());
  }

  async function insertDichVu(goiDichVuId) {
    const payload = listChooseDV.map((x) => ({
      hopDongKskId,
      boChiDinhId: goiDichVuId,
      dichVuId: x.dichVuId,
      soLuong: x.soLuong || 1,
      phongId: x.phongId,
      loaiHinhThanhToanId: x.loaiHinhThanhToanId,
      giaKhongBaoHiem: x.giaKhongBaoHiem,
      benhPhamId:
        !x?.benhPhamId && x?.dsBenhPhamId?.length == 1
          ? x?.dsBenhPhamId?.[0]
          : x.benhPhamId,
    }));
    insertMultipleDichVuChiTiet(payload).then((s) => {
      onClose();
      refreshList({ trongGoi: true });
    });
  }

  function onSelectGoiDV(item) {
    return () => {
      setSelectedGoiDV(item);
      onGetListService({ boChiDinhId: item.dichVuId });
    };
  }

  useEffect(() => {
    if (state.show && state.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH) {
      selectAllDV();
    }
  }, [state?.listDichVuTrongBo]);

  function selectAllDV(e) {
    const value = !e ? true : e.target.checked;
    if (value) {
      const _listChooseDV = [
        ...listDVKSKMemo
          .filter(
            (x1) =>
              listChooseDV.findIndex((x2) => x1.uniqueKey == x2.uniqueKey) == -1
          )
          .map((item) => {
            const loaiHinhThanhToanId = (item?.dsLoaiHinhThanhToan?.sort(
              (a, b) => b.uuTien - a.uuTien
            ) || [])[0]?.loaiHinhThanhToanId;
            return {
              ...item,
              soLuong: item.soLuong || 1,
              phongId: getPhongThucHienId(item, state.khoaId),
              loaiHinhThanhToanId,
              benhPhamId:
                !item?.benhPhamId && item?.dsBenhPhamId?.length == 1
                  ? item?.dsBenhPhamId?.[0]
                  : item.benhPhamId,
            };
          }),
        ...listChooseDV,
      ];

      updateData({
        listChooseDV: _listChooseDV,
      });
      e &&
        listDVKSKMemo.forEach((element) => {
          if (
            listChooseDV.findIndex((x) => element.dichVuId == x.dichVuId) > -1
          ) {
            message.error(
              `${t("khamSucKhoe.dichVu")} ${element.ten} ${t(
                "khamSucKhoe.daTonTai"
              )}.`
            );
          }
        });
    } else {
      updateData({
        listChooseDV: listChooseDV.filter(
          (x1) =>
            listDVKSKMemo.findIndex((x2) => x2.uniqueKey === x1.uniqueKey) ===
            -1
        ),
      });
    }
  }

  const isCheckedAll = useMemo(() => {
    if (!listDVKSKMemo || listDVKSKMemo.length === 0) return false;

    const found = listDVKSKMemo.every(
      (r) => listChooseDV.findIndex((x) => x.uniqueKey === r.uniqueKey) !== -1
    );

    return found;
  }, [listChooseDV, listDVKSKMemo]);

  function onChangeChooseItem(key, item) {
    return (e) => {
      let _listChooseDV = cloneDeep(listChooseDV);
      const _findDvIndex = _listChooseDV.findIndex(
        (x) => x.dichVuId === item.dichVuId
      );

      let value = e;
      if (key === "giaKhongBaoHiem") {
        value = e?.target.value && parseFloatNumber(e.target.value);
      }

      if (_findDvIndex !== -1) {
        _listChooseDV[_findDvIndex][key] = value;
        _listChooseDV[_findDvIndex].isNeedUpdate = true;

        updateData({
          listChooseDV: _listChooseDV,
        });
      }
    };
  }

  const columns = [
    {
      title: (
        <div>
          {state.loaiDichVu == LOAI_DICH_VU.BO_CHI_DINH && (
            <Checkbox checked={isCheckedAll} onChange={selectAllDV} />
          )}
        </div>
      ),
      width: 46,
      dataIndex: "checked",
      hideSearch: true,
      align: "center",
      render: (value, item, index) => {
        const _findIndex = listChooseDV.findIndex(
          (x) => x.uniqueKey === item.uniqueKey
        );

        return (
          <Checkbox
            id={"checkbox_dv_" + item.dichVuId}
            className="box-item"
            // onChange={onSelectService(item)}
            checked={_findIndex !== -1 ? true : false}
          />
        );
      },
    },
    {
      width: 120,
      dataIndex: "ma",
      type: true,
      hideSearch: true,
    },
    {
      width: 250,
      dataIndex: "ten",
      type: true,
      hideSearch: true,
    },
    {
      dataIndex: "donGia",
      type: true,
      width: 150,
      hideSearch: true,
    },
  ];

  function onDeleteItem(id) {
    let _listChooseDV = listChooseDV.filter((x) => x.uniqueKey !== id);

    updateData({
      listChooseDV: _listChooseDV,
    });
  }

  const onSearchService = (e) => {
    setState({ textSearchDv: e });

    // if (state.loaiDichVu !== LOAI_DICH_VU.BO_CHI_DINH) {
    onGetListService({
      loaiDichVu: state.loaiDichVu,
      timKiem: e,
      page: 0,
      size: 20,
    });
    // }
  };

  const onChangePage = (page, newSize = null) => {
    onGetListService({
      loaiDichVu: state.loaiDichVu,
      timKiem: state.textSearchDv,
      page: page - 1,
      size: newSize ? newSize : size,
    });
  };

  const onSizeChange = (value) => {
    onGetListService({
      loaiDichVu: state.loaiDichVu,
      timKiem: state.textSearchDv,
      page: 0,
      size: value,
    });
  };

  const { ma, ten } = formState;

  return (
    <ModalTemplate
      ref={refModal}
      width="90%"
      closable={false}
      onCancel={onClose}
      title={
        <div className="title">
          {isThemMoi
            ? t("goiDichVu.themMoiGoiDichVu")
            : t("goiDichVu.chinhSuaGoiDichVu")}
        </div>
      }
      actionLeft={<Button.QuayLai onClick={onClose} />}
      actionRight={
        <Button onClick={onSave} type="primary" rightIcon={<SVG.IcSave />}>
          {t("common.luu")}
        </Button>
      }
    >
      <Main>
        <div className="info-content">
          <div className="success-content">
            <Row gutter={[16, 16]}>
              <Col md={6}>
                <div className="input__wrapper">
                  {t("goiDichVu.maGoi")}:
                  <Input
                    className="input-goi"
                    value={ma}
                    onChange={(e) => handleChangeData(e.target.value, "ma")}
                    onBlur={(e) => onBlur(e.target.value, "ma")}
                  />
                  {/* {!validGoi.ma && (
                      <div className="error">Vui lòng nhập mã gói!</div>
                    )} */}
                </div>
              </Col>

              <Col md={18}>
                <div className="input__wrapper input__wrapper-2">
                  {t("goiDichVu.tenGoi")}:
                  <span style={{ color: "red" }}>*</span>
                  <Input
                    ref={refInputTenGoi}
                    className="input-goi"
                    value={ten}
                    onChange={(e) => handleChangeData(e.target.value, "ten")}
                    onBlur={(e) => onBlur(e.target.value, "ten")}
                  />
                  {!validGoi.ten && (
                    <div className="error">
                      {t("goiDichVu.vuiLongNhapTenGoi")}
                    </div>
                  )}
                </div>
              </Col>
            </Row>

            <Row gutter={[16, 16]}>
              <Col md={6}>
                <Select
                  onChange={onChangeGroupService}
                  defaultValue=""
                  value={state.loaiDichVu}
                  placeholder={t("khamSucKhoe.chonGoiDichVu")}
                  data={dataloaiDichVu}
                  className="select__goiDichVu"
                />
              </Col>

              <Col md={18}>
                <InputTimeout
                  className="searchDV"
                  placeholder={t("khamSucKhoe.nhapDeThemDichVu")}
                  value={state.textSearchDv}
                  onChange={onSearchService}
                  suffix={<SearchOutlined />}
                />
              </Col>
            </Row>
          </div>

          <Row className="content-goi-dich-vu" gutter={[16, 16]}>
            {state.loaiDichVu == LOAI_DICH_VU.BO_CHI_DINH && (
              <Col md={6} className="content-goi-dich-vu-item">
                <div className="header">{t("khamSucKhoe.boChiDinh")}</div>
                <div className="list-goi-dichvu">
                  <div>
                    <List
                      bordered
                      dataSource={listGoiDVMemo}
                      renderItem={(item) => (
                        <List.Item
                          className={
                            item.dichVuId === selectedGoiDV?.dichVuId
                              ? "select-goi-item"
                              : "goi-item"
                          }
                          onClick={onSelectGoiDV(item)}
                        >
                          <div>{item.ten}</div>
                        </List.Item>
                      )}
                    />
                  </div>
                </div>
              </Col>
            )}

            <Col
              md={state.loaiDichVu == LOAI_DICH_VU.BO_CHI_DINH ? 9 : 12}
              className="content-goi-dich-vu-item"
            >
              <div className="header">{t("common.dichVu")}</div>
              <div className="table-service">
                <TableWrapper
                  className="table"
                  rowKey={(record, index) => record.id || record.dichVuId}
                  onRow={onRow}
                  rowClassName={(record) =>
                    record?.checked
                      ? "background-checked"
                      : "checkbox_dv_" + record.dichVuId === state.idFocus
                      ? "background-hover"
                      : ""
                  }
                  columns={columns}
                  dataSource={listDVKSKMemo}
                ></TableWrapper>
                {state.loaiDichVu !== LOAI_DICH_VU.BO_CHI_DINH &&
                  !!listDVKSKMemo.length && (
                    <Pagination
                      listData={listDVKSKMemo}
                      onChange={onChangePage}
                      current={page + 1}
                      pageSize={size}
                      total={totalElements}
                      onShowSizeChange={onSizeChange}
                      stylePagination={{ justifyContent: "flex-start" }}
                    />
                  )}
              </div>
            </Col>
            <Col
              md={state.loaiDichVu == LOAI_DICH_VU.BO_CHI_DINH ? 9 : 12}
              className="content-goi-dich-vu-item content-goi-dich-vu__choose-content"
            >
              <DichVuDaChon
                data={listChooseDV}
                onDeleteItem={onDeleteItem}
                onChangeChooseItem={onChangeChooseItem}
                loaiDichVu={state.loaiDichVu}
              />
            </Col>
          </Row>
        </div>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalThemMoiGoiDichVu);
