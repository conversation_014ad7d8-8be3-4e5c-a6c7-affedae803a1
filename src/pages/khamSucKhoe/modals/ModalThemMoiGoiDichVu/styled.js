import styled from "styled-components";

export const Main = styled.div`
  padding: 12px 16px;
  @media screen and (min-width: 768px) {
    & .select__goiDichVu {
      width: 100%;
    }

    & .searchDV {
      width: calc(100% - 5px) !important;
      margin-left: 5px;
    }
    & .input__wrapper {
      display: flex;
      width: 100%;
      .input-goi {
        flex: 1;
      }
      &-2 {
        width: calc(100% - 5px) !important;
        margin-left: 5px;
      }
    }
  }

  .info-content {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    height: 100%;
  }

  .success-title {
    background: #fc3b3a;
    border-radius: 16px;
    padding: 8px 12px;
    margin: 16px;
    display: flex;
    align-items: center;

    span {
      color: #fff;
      font-size: 14px;
      font-weight: 900;
    }

    .check-icon {
      font-size: 24px;
      padding-right: 10px;
    }
  }

  .success-content {
    .ant-row {
      min-height: 40px;
    }
    .text {
      margin-bottom: 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1; /* number of lines to show */
      line-clamp: 1;
      -webkit-box-orient: vertical;
    }
  }

  .input-goi {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 6 6"><circle cx="3" cy="3" r="0.4" fill="black" /></svg>')
      20px;
    background-position-y: 7px;
    background-size: 5px 25px;
    border: none;
    width: 100%;
  }

  .ma-disable {
    pointer-events: none;
    background-color: rgba(0, 0, 0, 0.1);
  }

  .header {
    background-color: #0762f7;
    color: #fff;
    padding: 12px;
    font-size: 16px;
  }

  .table-service {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid #172b4d40;
    border-style: none solid solid solid;
  }

  .content-goi-dich-vu {
    height: 400px;
    .content-goi-dich-vu-item {
      height: 100%;
      display: flex;
      flex-direction: column;
      .ant-table-wrapper {
        height: 100%;
      }
    }
  }

  .select-goi-item {
    color: #fff;
    background-color: #0762f7;
  }

  .list-goi-dichvu {
    flex: 1;
    border: 1px solid #172b4d40;
    border-style: none solid solid solid;
    overflow: scroll;
  }
  .content-goi-dich-vu__choose-content {
    > div {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    .choose-content {
      height: unset !important;
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
`;

export const MainChoose = styled.div`
  .header {
    background-color: #049254;
    color: #fff;
    padding: 12px;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
  }

  .content {
    border: 2px dashed #049254;
    border-style: none dashed dashed dashed;

    .item {
      background: linear-gradient(
          0deg,
          rgba(23, 43, 77, 0.1),
          rgba(23, 43, 77, 0.1)
        ),
        #ffffff;
      border-radius: 3px;
      font-size: 14px;
      padding: 5px;
      margin: 5px;
    }
  }
`;
