import React, { useState, useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { message, Space, Tag } from "antd";
import Icon from "@ant-design/icons";
import moment from "moment";
import { query } from "redux-store/stores";
import { BaseSearch, Button } from "components";
import {
  getAllQueryString,
  setQueryStringValue,
} from "hooks/useQueryString/queryString";
import {
  useCache,
  useLazyKVMap,
  useLoading,
  useQueryAll,
  useStore,
  useThietLap,
} from "hooks";
import { SVG } from "assets";
import {
  TRANG_THAI_DO_SINH_HIEU,
  LOAI_PHONG,
  THIET_LAP_CHUNG,
  CACHE_KEY,
} from "constants/index";
import { isNumber } from "utils/index";

const TimKiem = (props) => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const [dataGOI_STT_PK_MH_SINH_HIEU, loadFinish] = useThietLap(
    THIET_LAP_CHUNG.GOI_STT_PK_MH_SINH_HIEU
  );
  const [state, _setState] = useState({
    dsPhongThucHienId: [],
    tuThoiGianVaoVien: moment()
      .set("hour", 0)
      .set("minute", 0)
      .set("second", 0),
    denThoiGianVaoVien: moment()
      .set("hour", 23)
      .set("minute", 59)
      .set("second", 59),
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const dataSearch = useStore("sinhHieu.dataSearch", []);
  const { data: listPhong, isLoading: isLoadingPhong } = useQueryAll(
    query.phong.queryAllPhong({
      params: {
        dsLoaiPhong: [LOAI_PHONG.PHONG_KHAM],
      },
    })
  );
  const [
    phongThucHienCache,
    setCachePhongThucHien,
    loadFinishPhongThucHienCache,
  ] = useCache("", CACHE_KEY.PHONG_THUC_HIEN_DS_SINH_HIEU, null, false);
  const [
    theoPhongKhamCache,
    setCacheTheoPhongKham,
    loadFinishTheoPhongKhamCache,
  ] = useCache("", CACHE_KEY.THEO_PHONG_KHAM_DS_SINH_HIEU, "false", false);

  const [getPhong] = useLazyKVMap(listPhong);

  const {
    sinhHieu: { searchSinhHieuByParams, exportExcel },
  } = useDispatch();

  useEffect(() => {
    if (
      isLoadingPhong ||
      !loadFinish ||
      !loadFinishTheoPhongKhamCache ||
      !loadFinishPhongThucHienCache
    )
      return;
    const { page, size, dataSortColumn, ...queries } = getAllQueryString();
    if (queries.trangThaiChiSoSong)
      queries.trangThaiChiSoSong = parseInt(queries.trangThaiChiSoSong);

    queries.theoPhongKham = queries.theoPhongKham
      ? queries.theoPhongKham
      : theoPhongKhamCache == "true"
        ? "true"
        : "false";

    if (dataGOI_STT_PK_MH_SINH_HIEU?.eval()) {
      if (queries.dsPhongThucHienId) {
        queries.dsPhongThucHienId = Number(queries.dsPhongThucHienId);
      } else {
        queries.dsPhongThucHienId = isNumber(phongThucHienCache)
          ? Number(phongThucHienCache)
          : listPhong[0]?.id;
      }
    } else {
      if (queries.dsPhongThucHienId) {
        queries.dsPhongThucHienId = queries.dsPhongThucHienId
          .split(",")
          .map(Number);
      } else {
        queries.dsPhongThucHienId = listPhong.map((item) => item.id);
      }
    }

    setState(queries);
  }, [
    isLoadingPhong,
    listPhong,
    dataGOI_STT_PK_MH_SINH_HIEU,
    loadFinish,
    theoPhongKhamCache,
    phongThucHienCache,
    loadFinishTheoPhongKhamCache,
    loadFinishPhongThucHienCache,
  ]);

  const onClickXuatDS = async () => {
    try {
      showLoading();
      await exportExcel(dataSearch);
    } catch {
    } finally {
      hideLoading();
    }
  };
  const onSearch = (type) => (data) => {
    if (type == "thoiGianVaoVien") {
      searchSinhHieuByParams({
        tuThoiGianVaoVien: data.tuThoiGianVaoVien?.format(
          "YYYY-MM-DD 00:00:00"
        ),
        denThoiGianVaoVien: data.denThoiGianVaoVien?.format(
          "YYYY-MM-DD 23:59:59"
        ),
      });
    }
  };
  const listTheoPhongKham = [
    {
      id: "false",
      ten: t("khamBenh.danhSachNb"),
    },
    {
      id: "true",
      ten: t("sinhHieu.danhSachNBTheoPhongKham"),
    },
  ];

  const onChangeInputSearch = (obj) => {
    setState(obj);
    searchSinhHieuByParams(obj);
  };

  const onClickGoiSoPhongKham = () => {
    if (!state.dsPhongThucHienId) {
      message.error(t("sinhHieu.vuiLongChonPhongKham"));
      return;
    }
    window.open("/qms-kham-benh/goi-nguoi-benh/" + state.dsPhongThucHienId?.[0])
  };

  return (
    <>
      <BaseSearch
        cacheData={state}
        dataInput={[
          {
            widthInput: "200px",
            keyValueInput: "theoPhongKham",
            functionChangeInput: (data) => {
              setCacheTheoPhongKham(data.theoPhongKham);
              onChangeInputSearch(data);
            },
            type: "select",
            listSelect: listTheoPhongKham,
          },
          ...(dataSearch.theoPhongKham == "true" &&
            !dataGOI_STT_PK_MH_SINH_HIEU?.eval()
            ? [
              {
                key: "dsPhongThucHienId",
                widthInput: "220px",
                keyValueInput: "dsPhongThucHienId",
                type: "virtualizedSelect",
                placeholder: t("dashboard.phongKham"),
                options: listPhong,
                value: state.dsPhongThucHienId,
                showConfirmButton: true,
                dropdownWidth: 300,
                functionChangeInput: (data) => {
                  setState(data);
                  searchSinhHieuByParams(data);
                },
              },
            ]
            : []),
          ...(dataSearch.theoPhongKham == "true" &&
            dataGOI_STT_PK_MH_SINH_HIEU?.eval()
            ? [
              {
                key: "dsPhongThucHienId",
                widthInput: "220px",
                keyValueInput: "dsPhongThucHienId",
                type: "select",
                placeholder: t("dashboard.phongKham"),
                options: listPhong,
                listSelect: listPhong,
                functionChangeInput: (data) => {
                  setCachePhongThucHien(data.dsPhongThucHienId);
                  onChangeInputSearch(data);
                },
              },
            ]
            : []),
          {
            widthInput: "200px",
            customWidth: "180px",
            type: "dateOptions",
            state: state,
            setState: setState,
            keyValueInput: ["tuThoiGianVaoVien", "denThoiGianVaoVien"],
            functionChangeInput: onSearch("thoiGianVaoVien"),
            title: t("tiepDon.ngayDangKy"),
            placeholder: t("tiepDon.ngayDangKy"),
            format: "DD/MM/YYYY",
          },
          {
            widthInput: "300px",
            placeholder: t("dieuTriDaiHan.timHoTenQrMaNbMaHs"),
            isScanQR: true,
            qrGetValue: "maNb",
            keysFlexible: [
              {
                key: "tenNb",
                type: "string",
              },
              {
                key: "maHoSo",
                type: "maHoSo",
              },
              {
                key: "maBenhAn",
                type: "number7",
              },
              {
                key: "maNb",
                type: "startLettersEndNumbers",
              },
              {
                key: "maThe",
                type: "hex",
              },
            ],
            functionChangeInput: onChangeInputSearch,
          },
          {
            widthInput: "180px",
            placeholder: t("sinhHieu.timSoDienThoai"),
            keyValueInput: "soDienThoai",
            functionChangeInput: onChangeInputSearch,
          },
          {
            widthInput: "120px",
            placeholder: t("common.trangThai"),
            keyValueInput: "trangThaiChiSoSong",
            functionChangeInput: onChangeInputSearch,
            type: "select",
            listSelect: TRANG_THAI_DO_SINH_HIEU,
          },
        ]}
        componentRight={
          <div className="flex gap-8">
            <Button
              rightIcon={<SVG.IcList />}
              iconHeight={15}
              type="primary"
              onClick={onClickXuatDS}
            >
              {t("sinhHieu.xuatDS")}
            </Button>
            {state.theoPhongKham == "true" &&
              dataGOI_STT_PK_MH_SINH_HIEU?.eval() && (
                <Button type="success" onClick={onClickGoiSoPhongKham}>
                  {t("sinhHieu.goiSoPhongKham")}
                </Button>
              )}
          </div>
        }
      />
      {state.theoPhongKham == "true" &&
        !dataGOI_STT_PK_MH_SINH_HIEU?.eval() && (
          <Space className="tag-phong-kham-container" size={0}>
            {state.dsPhongThucHienId?.map((item) => {
              return (
                <Tag
                  closable
                  onClose={() => {
                    const dsPhongThucHienId = state.dsPhongThucHienId.filter(
                      (i) => i !== item
                    );
                    setQueryStringValue("dsPhongThucHienId", dsPhongThucHienId);
                    setState({
                      dsPhongThucHienId,
                    });
                    searchSinhHieuByParams({
                      dsPhongThucHienId,
                    });
                  }}
                  icon={
                    isLoadingPhong && <Icon component={SVG.IcReload} spin />
                  }
                  key={item}
                >
                  {getPhong(item)?.ten}
                </Tag>
              );
            })}
          </Space>
        )}
    </>
  );
};

export default TimKiem;
