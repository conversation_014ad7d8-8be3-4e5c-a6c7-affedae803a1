import React, {
  useState,
  forwardRef,
  memo,
  useContext,
  useEffect,
  useMemo,
  useRef,
} from "react";
import { Row, Col, message } from "antd";
import QuayTiepDon from "./QuayTiepDon";
import ButtonNguoiBenhTiepTheo from "../ButtonNguoiBenhTiepTheo";
import { Button, InputTimeout, Select, AuthWrapper, Tooltip } from "components";
import { useDispatch } from "react-redux";
import { Main } from "./styled";
import { addPrefixNumberZero, isArray } from "utils";
import moment from "moment";
import {
  ROLES,
  LENGTH_ZERO_PREFIX,
  LOAI_QUAY,
  CACHE_KEY,
  THIET_LAP_CHUNG,
  DOI_TUONG,
  DS_TINH_CHAT_KHOA,
  ENUM,
} from "constants/index";
import { useTranslation } from "react-i18next";
import { useListAll, useStore, useCache, useThietLap, useEnum } from "hooks";
import TiepDonContext from "pages/tiepDon/context/TiepDon";
import { SVG } from "assets";
import ErrorValidateMessage from "../ErrorValidateMessage";
import { selectMaTen } from "redux-store/selectors";
import NhapChieuCao from "../ThongTinSinhHieu/NhapChieuCao";
import classNames from "classnames";

const DanhSachBenhNhan = ({ ...props }, ref) => {
  const {
    disableTiepDon,
    nbDotDieuTriId,
    isTiepDonNoiTru,
    onGiamDinhThe,
    isTiemChung,
    isTiepDonHenDieuTri,
  } = useContext(TiepDonContext);
  const { t } = useTranslation();
  const refTimeoutSearchSTT = useRef(null);
  const {
    goiSo: { dongQuay, searchGoiSo, updateData: updateDataGoiSo },
    khoa: { getListKhoaTongHop },
    tiepDon: {
      updateData,
      updateThongTinChanDoan,
      resetData,
      updateThongTinNb,
    },
    kiosk: { createOrEdit },
  } = useDispatch();

  const [existingQmsKioskId, setExistingQmsKioskId] = useState(false);
  const [dataBAT_BUOC_NHAP_CAN_NANG] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_CAN_NANG
  );
  const [dataHIEN_THI_CHON_DOI_TUONG_KCB_TIEP_DON_NOI_TRU] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CHON_DOI_TUONG_KCB_TIEP_DON_NOI_TRU
  );
  const [TIEP_DON_STT_THEO_BLOCK] = useThietLap(
    THIET_LAP_CHUNG.TIEP_DON_STT_THEO_BLOCK
  );
  const { readOnlyDsGoiNho, quayTiepDonId } = useStore(
    "goiSo",
    {},
    { fields: "readOnlyDsGoiNho, quayTiepDonId" }
  );
  const nbTiepTheo = useStore("goiSo.nbTiepTheo");
  const currentKiosk = useStore("kiosk.currentKiosk", {});
  const [listDoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const dsDangTiepDon = useStore("goiSo.dsDangTiepDon", []);

  const [cacheCurrentQuay, setCacheCurrentQuay] = useCache(
    "",
    CACHE_KEY.DATA_CURRENT_QUAY,
    null,
    false
  );

  const {
    stt,
    tuoi,
    canNang: canNangTiepDon,
    doiTuong,
    doiTuongKcb,
  } = useStore(
    "tiepDon",
    {},
    { fields: "stt,tuoi,canNang,doiTuong,doiTuongKcb" }
  );
  const canNangChiSoSong = useStore("tiepDon.nbChiSoSong.canNang", null);
  const canNang = !nbDotDieuTriId ? canNangChiSoSong : canNangTiepDon;

  const disabled = disableTiepDon;

  const { dsCdVaoVienId, khoaNhapVienId } = useStore("tiepDon.nbTongKetRaVien");
  const listDataTongHop = useStore("khoa.listDataTongHop");
  const [listAllMaBenh] = useListAll("maBenh", {}, true);

  const isBatBuocNhapChieuCao = useMemo(() => {
    return !!listAllMaBenh.find((maBenh) => maBenh.id === dsCdVaoVienId?.[0])
      ?.chieuCao;
  }, [dsCdVaoVienId]);

  // Cho tiếp đón nội trú
  // requireCanNang: true áp dụng cho cả 2 đối tượng , false chỉ áp dụng bhyt
  const requireCanNang = useMemo(() => {
    const [tuoiBatBuoc, requireCanNang] = dataBAT_BUOC_NHAP_CAN_NANG
      .split("/")
      .map((str) => str?.eval());

    return (
      (tuoi || 0) < tuoiBatBuoc &&
      (requireCanNang || doiTuong === DOI_TUONG.BAO_HIEM) &&
      !canNang
    );
  }, [dataBAT_BUOC_NHAP_CAN_NANG, doiTuong, tuoi, canNang]);

  const { showDoiTuongKcb, doiTuongKcbMacDinh } = useMemo(() => {
    let showDoiTuongKcb = false;
    let doiTuongKcbMacDinh;

    const cauHinhRaw = dataHIEN_THI_CHON_DOI_TUONG_KCB_TIEP_DON_NOI_TRU
      ?.toString()
      .trim();

    if (
      !cauHinhRaw ||
      cauHinhRaw.toUpperCase() === "FALSE" ||
      !isTiepDonNoiTru ||
      !isArray(listDataTongHop, true)
    ) {
      return { showDoiTuongKcb, doiTuongKcbMacDinh };
    }

    const [flag, dsMaKhoaRaw] = cauHinhRaw.split("/");

    if (flag.toUpperCase() !== "TRUE") {
      return { showDoiTuongKcb, doiTuongKcbMacDinh };
    }

    showDoiTuongKcb = true;

    const dsMaKhoa = (dsMaKhoaRaw || "")
      .split(",")
      .map((s) => s.trim())
      .filter(Boolean);

    const khoaNhapVien = listDataTongHop.find(
      (khoa) => khoa.id === khoaNhapVienId
    );

    const maKhoaNhapVien = khoaNhapVien?.ma;

    if (maKhoaNhapVien && dsMaKhoa.includes(maKhoaNhapVien)) {
      doiTuongKcbMacDinh = 2;
    } else {
      doiTuongKcbMacDinh = 3;
    }

    return { showDoiTuongKcb, doiTuongKcbMacDinh };
  }, [
    dataHIEN_THI_CHON_DOI_TUONG_KCB_TIEP_DON_NOI_TRU,
    listDataTongHop,
    khoaNhapVienId,
    isTiepDonNoiTru,
  ]);

  const listDoiTuongKcbNoiTru = useMemo(() => {
    return (listDoiTuongKcb || []).filter((item) =>
      [2, 3, 4].includes(item.id)
    );
  }, [listDoiTuongKcb]);

  useEffect(() => {
    if (showDoiTuongKcb && doiTuongKcbMacDinh && !nbDotDieuTriId) {
      updateData({ doiTuongKcb: doiTuongKcbMacDinh });
    }
  }, [showDoiTuongKcb, doiTuongKcbMacDinh, nbDotDieuTriId]);

  const onKeyDownStt = (event) => {
    if (event.nativeEvent.key === "Enter") {
      onSearch(stt);
    }
  };

  const onSearch = (stt) => {
    searchGoiSo({ stt }, false)
      .then(async (data) => {
        if (!data.gioiTinh) await checkGender(data);
        if (data.maTheBhyt)
          onGiamDinhThe &&
            onGiamDinhThe({
              data: {
                hoTen: data.tenNb,
                ngaySinh: data.ngaySinh,
                maThe: data.maTheBhyt,
              },
              tenNb: data.tenNb,
              diaChi: data.diaChi,
            });
      })
      .catch((e) => {});
  };

  const onResetData = () => {
    if (stt && quayTiepDonId) {
      huyTiepDon(quayTiepDonId);
    }
    resetData();
  };

  useEffect(() => {
    if (disabled) {
      updateDataGoiSo({ readOnlyDsGoiNho: true });
    }
  }, [disabled]);

  useEffect(() => {
    getListKhoaTongHop({
      page: "",
      size: "",
      active: true,
      dsTinhChatKhoa: DS_TINH_CHAT_KHOA.NOI_TRU,
    });
    return () => {
      //unmount
      if (refTimeoutSearchSTT.current) {
        //clear timeout search stt
        clearTimeout(refTimeoutSearchSTT.current);
      }
    };
  }, []);

  const onChangeSTT = (value) => {
    if (value === null) {
      updateData({ stt: null });
    } else {
      if (value == "") {
        onResetData();
      } else {
        updateData({ stt: Number(value) });
        onSearch(value);
      }
    }
  };

  const onClose = () => {
    onChangeSTT(null);
    if (!disabled && quayTiepDonId) {
      dongQuay({ quayHienTai: quayTiepDonId }).then(() => resetData());
    }
  };

  const checkGender = (data) => {
    let genderVan = data.tenNb.search(" VĂN ");
    let genderThi = data.tenNb.search(" THỊ ");
    if (genderVan >= 0 && genderThi < 0) {
      updateData({ gioiTinh: 1 });
    } else if (genderThi >= 0) {
      updateData({ gioiTinh: 2 });
    } else {
      updateData({ gioiTinh: "" });
    }
  };

  const onChange = (variables) => (value) => {
    if (variables == "khoaNhapVienId" || variables == "dsCdVaoVienId") {
      if (variables == "khoaNhapVienId" && !nbDotDieuTriId) {
        updateThongTinNb({ khoaChiDinhId: value }, "nbChiSoSong");
      }
      updateThongTinChanDoan({
        [variables]:
          variables === "khoaNhapVienId" ? value : value ? [value] : [],
      });
    } else {
      if (variables == "canNang") {
        if (!nbDotDieuTriId) {
          updateThongTinNb({ canNang: value }, "nbChiSoSong");
        } else {
          updateData({ canNang: value });
        }
      } else {
        updateData({
          [variables]: value,
        });
      }
    }
  };

  const handleCurrentQuay = (data) => {
    setCacheCurrentQuay(data);
  };

  const handleExistingQmsKioskId = (data) => {
    setExistingQmsKioskId(data);
  };

  const handleQuaySync = () => {
    if (existingQmsKioskId) {
      message.error(t("tiepDon.chuaCoThongTinKioskId"));
      return;
    }
    const { dsQuayId = [] } = currentKiosk;
    const hasMultipleQuays = dsQuayId.length > 1;
    const includesCacheQuay = dsQuayId.includes(cacheCurrentQuay);
    const includesQuayTiepDon = dsQuayId.includes(quayTiepDonId);

    const updatedQuayIds = dsQuayId.map((item) =>
      item === cacheCurrentQuay ? quayTiepDonId : item
    );

    const newDsQuayId = hasMultipleQuays
      ? includesCacheQuay && !includesQuayTiepDon
        ? updatedQuayIds
        : includesQuayTiepDon
        ? dsQuayId
        : [...dsQuayId, quayTiepDonId]
      : [quayTiepDonId];

    createOrEdit({
      ...currentKiosk,
      dsQuayId: newDsQuayId,
    });
  };
  const renderStt = () => {
    if (dsDangTiepDon?.length) {
      const first = Math.min(...dsDangTiepDon.map((x) => x.stt));
      const last = Math.max(...dsDangTiepDon.map((x) => x.stt));
      const text = `${first} - ${last}`;
      return text;
    }
    return "";
  };
  return (
    <Main className="container" {...props} ref={ref}>
      {nbDotDieuTriId && !isTiepDonHenDieuTri ? (
        <Row gutter={[6, 0]}>
          <Col
            md={24}
            xl={24}
            xxl={24}
            className="label"
            style={{
              height: isTiemChung ? 38 : 56,
              marginTop: isTiemChung ? 46 : 0, // tiemChung hard code
            }}
          >
            {t("thietLap.soLuotGioiThieu")}: 0
          </Col>
          {/* {!isTiepDonNoiTru && ( */}
          <Col md={24} xl={24} xxl={24} style={{ marginLeft: 0, height: 56 }}>
            <label>{t("tiepDon.chonQuay")}</label>
            <AuthWrapper accessRoles={[ROLES["TIEP_DON"].CHON_QUAY]}>
              <QuayTiepDon
                disabled={disabled}
                disabledNoiTru={isTiepDonNoiTru}
                handleCurrentQuay={handleCurrentQuay}
                handleExistingQmsKioskId={handleExistingQmsKioskId}
              />
            </AuthWrapper>
          </Col>
          {/* )} */}
          {isTiepDonNoiTru && (
            <Row>
              <Col
                md={12}
                xl={12}
                xxl={12}
                className="item-input"
                style={{ marginLeft: 0, height: 56 }}
              >
                <label
                  className={requireCanNang ? `label label-error` : "label"}
                >
                  {t("sinhHieu.canNang")}
                  <span style={{ color: "red" }}> *</span>
                </label>
                <InputTimeout
                  onChange={onChange("canNang")}
                  placeholder={t("sinhHieu.canNang")}
                  disabled={disableTiepDon}
                  value={canNang}
                  type="number"
                />
                <ErrorValidateMessage
                  isError={requireCanNang}
                  message={t("lapBenhAn.vuiLongNhapCanNang")}
                />
              </Col>
              <NhapChieuCao
                md={12}
                xl={12}
                xxl={12}
                required={isBatBuocNhapChieuCao}
              />
            </Row>
          )}
        </Row>
      ) : !isTiepDonNoiTru ? (
        <>
          <Row md={24} xl={24} xxl={24} className="first-row">
            <div className="label">
              {t("tiepDon.chonQuay")}
              <Tooltip title={t("tiepDon.dongBoQuay")}>
                <SVG.IcReload
                  className={classNames("sync-icon", {
                    disabled: !quayTiepDonId,
                  })}
                  onClick={handleQuaySync}
                />
              </Tooltip>
            </div>
          </Row>
          <Row className="second-row" style={{ paddingBottom: 6 }}>
            <Col md={15} xs={15} xl={15} xxl={15}>
              <AuthWrapper accessRoles={[ROLES["TIEP_DON"].CHON_QUAY]}>
                <QuayTiepDon
                  disabled={disabled}
                  handleCurrentQuay={handleCurrentQuay}
                  handleExistingQmsKioskId={handleExistingQmsKioskId}
                />
              </AuthWrapper>
              <AuthWrapper accessRoles={[ROLES["TIEP_DON"].NB_TIEP_THEO]}>
                <ButtonNguoiBenhTiepTheo
                  tiepDonMoi={true}
                  fit={true}
                  disabled={disabled}
                  className={`btn-nb-tiep-theo ${
                    readOnlyDsGoiNho || disabled ? " disable-button" : ""
                  }`}
                  notPostNbTiepTheoId={true}
                  type="primary"
                />
              </AuthWrapper>
            </Col>
            <Col md={9} xs={9} xl={9} xxl={9}>
              <div className="item-input">
                <InputTimeout
                  onChange={onChangeSTT}
                  placeholder={t("tiepDon.nhapSTTTiepDon")}
                  onKeyDown={onKeyDownStt}
                  disabled={disableTiepDon || disabled}
                  value={addPrefixNumberZero(stt, LENGTH_ZERO_PREFIX)}
                  allowClear={true}
                />
              </div>
              <div className="mt-10">
                <Button
                  className="btn-dong-quay"
                  onClick={onClose}
                  type="default"
                  fit={true}
                  leftIcon={<SVG.IcCancel />}
                  iconHeight={20}
                  color="#64748B"
                >
                  {t("tiepDon.dongQuay")}
                </Button>
              </div>
            </Col>
          </Row>
          {TIEP_DON_STT_THEO_BLOCK?.eval() ? (
            <Row className="second-row">
              <Col md={24} xl={24} xxl={24}>
                <div className="elipsis">{renderStt()}</div>
              </Col>
            </Row>
          ) : (
            <Row className="second-row">
              <Col md={24} xl={24} xxl={24}>
                <div className="elipsis">
                  {nbTiepTheo?.stt && (
                    <b>{addPrefixNumberZero(nbTiepTheo?.stt)} </b>
                  )}
                  {nbTiepTheo?.tenNb && <span>{nbTiepTheo?.tenNb}</span>}
                  {nbTiepTheo?.ngaySinh && (
                    <span>
                      {" - "}
                      {moment(nbTiepTheo?.ngaySinh)?._d?.getAge()}T
                    </span>
                  )}
                </div>
              </Col>
            </Row>
          )}
        </>
      ) : (
        <Row className="nhap-vien" span={24}>
          <Col span={24} className="header-item">
            <Row span={24} gutter={[6, 0]}>
              <Col span={12} style={{ marginLeft: "-6px" }}>
                <div className="item-select">
                  <label
                    className={!khoaNhapVienId ? `label label-error` : "label"}
                  >
                    {t("common.khoaNhapVien")}
                    <span style={{ color: "red" }}> *</span>
                  </label>
                  <Select
                    onChange={onChange("khoaNhapVienId")}
                    className="item__second-select"
                    placeholder={t("common.khoaNhapVien")}
                    data={listDataTongHop}
                    disabled={disableTiepDon}
                    value={khoaNhapVienId}
                  />
                  <ErrorValidateMessage
                    isError={!khoaNhapVienId}
                    message={t("lapBenhAn.vuiLongChonKhoaNhapVien")}
                  />
                </div>
              </Col>
              <Col span={6}>
                <div className="item-select">
                  <label
                    className={requireCanNang ? `label label-error` : "label"}
                  >
                    {t("sinhHieu.canNang")}
                    <span style={{ color: "red" }}> *</span>
                  </label>
                  <InputTimeout
                    onChange={onChange("canNang")}
                    placeholder={t("sinhHieu.canNang")}
                    disabled={disableTiepDon}
                    value={canNang}
                    type="number"
                  />
                  <ErrorValidateMessage
                    isError={requireCanNang}
                    message={t("lapBenhAn.vuiLongNhapCanNang")}
                  />
                </div>
              </Col>
              <NhapChieuCao
                md={6}
                xl={6}
                xxl={6}
                required={isBatBuocNhapChieuCao}
              />
            </Row>
          </Col>
          <Col
            span={24}
            className="header-item"
            {...(isTiepDonNoiTru && !nbDotDieuTriId
              ? { style: { marginTop: "-15px" } }
              : {})}
          >
            <div className="item-select">
              <label
                className={
                  !dsCdVaoVienId?.length ? `label label-error` : "label"
                }
              >
                {t("quanLyNoiTru.chanDoanVaoVien")}
                <span style={{ color: "red" }}> *</span>
              </label>
              <Select
                onChange={onChange("dsCdVaoVienId")}
                className="item__second-select"
                placeholder={t("quanLyNoiTru.chanDoanVaoVien")}
                disabled={disableTiepDon}
                data={listAllMaBenh}
                getLabel={selectMaTen}
                value={dsCdVaoVienId}
              />
              <ErrorValidateMessage
                isError={!dsCdVaoVienId?.length}
                message={t("lapBenhAn.vuiLongChonChanDoanNhapVien")}
              />
            </div>
          </Col>
          {showDoiTuongKcb && (
            <Col span={24} className="header-item">
              <div className="item-select" style={{ marginBottom: 0 }}>
                <label className="label">
                  {t("tiepDon.doiTuongKcbLapBenhAn")}
                </label>
                <Select
                  onChange={onChange("doiTuongKcb")}
                  className="item__second-select"
                  disabled={disableTiepDon}
                  value={doiTuongKcb}
                  data={listDoiTuongKcbNoiTru}
                  allowClear={true}
                />
              </div>
            </Col>
          )}
        </Row>
      )}
    </Main>
  );
};

export default memo(forwardRef(DanhSachBenhNhan));
