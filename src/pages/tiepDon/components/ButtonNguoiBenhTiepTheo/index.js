import React, { useEffect, memo, useRef, forwardRef } from "react";
import { Main } from "./styled";
import { message } from "antd";
import { useSelector, useDispatch } from "react-redux";
import { useInterval, useStore, useThietLap } from "hooks";
import { useTranslation } from "react-i18next";
import { HOTKEY, LOAI_QUAY, THIET_LAP_CHUNG } from "constants/index";
import { useContext } from "react";
import TiepDonContext from "pages/tiepDon/context/TiepDon";
import { SVG } from "assets";
import { useFilterQuay } from "components/ChonQuay";

const ButtonNguoiBenhTiepTheo = (
  {
    disabled,
    fit,
    type = "primary",
    notPostNbTiepTheoId = false,
    tiepDonMoi = true,
    useHotkey = true,
    rightContent,
    ...props
  },
  ref
) => {
  const { t } = useTranslation();
  const { layerId, onGiamDinhThe } = useContext(TiepDonContext);
  const refButton = useRef(null);
  const {
    goiSo: { getNbTiepTheo, updateData: updateDataGoiSo },
    tiepDon: { updateData },
    phimTat: { onRegisterHotkey },
    qms: { getGoiSoConfig },
  } = useDispatch();

  const nbTiepTheo = useStore("goiSo.nbTiepTheo");

  const quayTiepDonId = useSelector((state) => state.goiSo.quayTiepDonId);
  const [TIEP_DON_STT_THEO_BLOCK] = useThietLap(
    THIET_LAP_CHUNG.TIEP_DON_STT_THEO_BLOCK
  );
  const { data: listAllQuayTiepDon } = useFilterQuay({
    dsLoai: LOAI_QUAY.TIEP_DON,
    filterTheoNgayHoatDong: true,
  });

  useInterval(() => {
    {
      /*
        Cứ 10s là load lên người bệnh hiện trên nút người bệnh tiếp theo
        - trong trường hợp đã chọn quầy và chưa có người bệnh nào thì mới load.
      */
    }
    //Nếu đã chọn quầy tiếp đón
    if (quayTiepDonId) onGetNbTiepTheo();
  }, 5000);

  useEffect(() => {
    {
      /*
      - Nếu chưa chọn quầy mà đã có nbTiepTheo thì set nbTiepTheo = null
      - Nếu đã chọn quầy mà chưa có nbTiepTheo thì gọi api get NbTiepTheo
      */
    }
    if (!quayTiepDonId) {
      if (nbTiepTheo?.id) updateDataGoiSo({ nbTiepTheo: {} });
    } else {
      getGoiSoConfig();
      onGetNbTiepTheo();
    }
  }, [nbTiepTheo, quayTiepDonId]);

  const onSetNbTiepTheo = (isLoadNguoiBenhTiepDon) => {
    if (quayTiepDonId) {
      let data = {
        id: quayTiepDonId,
        isLoadNguoiBenhTiepDon,
        data: notPostNbTiepTheoId
          ? {}
          : {
              nbTiepTheo: nbTiepTheo?.id,
            },
      };
      if (TIEP_DON_STT_THEO_BLOCK?.eval()) {
        const quayTiepDon = listAllQuayTiepDon?.find(
          (x) => x.id == quayTiepDonId
        );
        data = {
          id: quayTiepDonId,
          data: { soLuong: quayTiepDon?.soLuongHangDoi },
        };
      }
      updateData({
        thongTinThanhToan: {
          daThanhToan: true,
          messageChuaThanhToan: "",
        },
      });
      if (tiepDonMoi)
        getNbTiepTheo(data).then((s) => {
          if (!s?.data) return message.error(t("tiepDon.daHetSttDoiVaoQuay"));
          const dangTiepDon = s?.data?.dsDangTiepDon?.[0];
          if (dangTiepDon?.maTheBhyt) {
            onGiamDinhThe &&
              onGiamDinhThe({
                data: {
                  hoTen: dangTiepDon?.tenNb,
                  ngaySinh: dangTiepDon?.ngaySinh,
                  maThe: dangTiepDon?.maTheBhyt,
                },
                tenNb: dangTiepDon?.tenNb,
                diaChi: dangTiepDon?.diaChi,
              });
          }
          if (s?.data?.dsDangTiepDon) {
            updateDataGoiSo({ dsDangTiepDon: s?.data?.dsDangTiepDon });
          }
        });
    } else message.error(t("tiepDon.vuiLongChonQuayTiepDonTruocKhiGoiSo"));
  };

  const onGetNbTiepTheo = () => {
    if (!nbTiepTheo?.id)
      getNbTiepTheo({
        id: quayTiepDonId,
        isGet: true,
      });
  };
  useEffect(() => {
    if (useHotkey)
      onRegisterHotkey({
        layerId: layerId,
        hotKeys: [
          {
            keyCode: HOTKEY.F1, //F1
            onEvent: () => {
              refButton.current && refButton.current.click();
            },
          },
        ],
      });
  }, []);

  const onClick = () => {
    if (props.onClick) {
      props.onClick();
    } else {
      onSetNbTiepTheo(true);
    }
  };

  return (
    <Main
      className={`${props.className || ""}`}
      id="btn_nguoi_benh_tiep_theo"
      onClick={onClick}
      type={type}
      fit={fit}
      rightIcon={<SVG.IcExtend />}
      disabled={disabled}
      rightContent={rightContent}
      ref={(el) => {
        if (ref?.hasOwnProperty("current")) {
          ref.current = el;
        }
        refButton.current = el;
      }}
    >
      <span>{t("tiepDon.nbTiepTheo")}</span>
      <span className="phim-tat">[F1]</span>
    </Main>
  );
};

export default memo(forwardRef(ButtonNguoiBenhTiepTheo));
