import React, { memo, useMemo, forwardRef, useState } from "react";
import moment from "moment";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { isEqual } from "lodash";
import { Header<PERSON>earch, TableWrapper } from "components";
import { useLazyKVMap, useThietLap, useWindowSize } from "hooks";
import TableEmpty from "pages/kho/components/TableEmpty";
import { roundToDigits } from "utils";
import {
  LOAI_NHAP_XUAT,
  THEO_SO_LUONG_TON_KHO,
  THIET_LAP_CHUNG,
  TRANG_THAI_PHIEU_NHAP_XUAT,
} from "constants/index";
import { SVG } from "assets";
import { customSortBySttAndName } from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuLinh/utils";
import DropdownSoLo from "pages/nhaThuoc/ChiTietDonThuoc/LeftPanel/DanhSach/DropdownSoLo";
import khoTonKhoProvider from "data-access/kho/kho-ton-kho-provider";
import { Main } from "./styled";

const DanhSachHangHoa = (
  { onFocusSearchHangHoa, isEdit, isEditSoLo, ...props },
  ref
) => {
  const [selectedId, setSelectedId] = useState(null);
  const { dsNhapXuatChiTiet = [], thongTinPhieu } = useSelector(
    (state) => state.phieuNhapXuat
  );
  const {
    phieuNhapXuat: { onRemoveItem, updateData },
  } = useDispatch();
  const { t } = useTranslation();
  const [dataAN_THONG_TIN_HSD_VA_SO_LO] = useThietLap(
    THIET_LAP_CHUNG.AN_THONG_TIN_HSD_VA_SO_LO
  );
  const size = useWindowSize();

  const dsNhapXuatChiTietFilterMemo = useMemo(() => {
    let result = dsNhapXuatChiTiet;
    if (
      [
        TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI,
        TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI_DA_GIU_CHO,
      ].includes(thongTinPhieu?.trangThai)
    ) {
      result = result.filter((item) => item?.soLuong !== 0);
    }
    if (!isEdit) {
      result = customSortBySttAndName(result, ["sttPhieuLinh", "ten"]).map(
        (item, index) => ({ ...item, index: index + 1 })
      );
    }
    return result;
  }, [dsNhapXuatChiTiet, thongTinPhieu, isEdit]);

  const [getPhieuNhapXuat] = useLazyKVMap(dsNhapXuatChiTiet, "id");
  const onDelete = (item, index) => (e) => {
    onRemoveItem({ item });
  };

  const soLuongSoCap = (sl, dv) => {
    return roundToDigits((sl || 0) / (dv || 1), 3);
  };

  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        setSelectedId(record.id);
      },
    };
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: size.width <= 1400 ? 64 : 64,
      dataIndex: "index",
      key: "index",
      hideSearch: true,
      align: "center",
    },
    {
      title: <HeaderSearch title={t("kho.tenHangHoa")} sort_key="ten" />,
      width: 230,
      dataIndex: "ten",
      key: "ten",
      type: true,
      hideSearch: false,
      show: true,
      i18Name: "kho.tenHangHoa",
      render: (value, item, index) => {
        return (
          <>
            <span
              className=""
              style={{
                color: "#0762F7",
                fontWeight: "bold",
                display: "inline-block",
              }}
            >
              {item?.ma} - {item?.ten}
            </span>
            <div>{item?.ghiChu}</div>
          </>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.sLThuCapYeuCau")}
          sort_key="soLuongYeuCau"
        />
      ),
      key: "sLThuCapYeuCau",
      width: size.width <= 1400 ? 83 : 83,
      dataIndex: "soLuongYeuCau",
      hideSearch: true,
      align: "right",
      show: true,
      i18Name: "kho.sLThuCapYeuCau",
      render: (field, item, index) => {
        return field ? `${field} ${item.tenDonViTinh || ""}` : 0;
      },
    },
    {
      title: (
        <HeaderSearch title={t("kho.sLsoCapYeuCau")} sort_key="soLuongSoCap" />
      ),
      key: "sLsoCapYeuCau",
      width: size.width <= 1400 ? 83 : 83,
      dataIndex: "soLuongSoCap",
      hideSearch: true,
      align: "right",
      show: true,
      i18Name: "kho.sLsoCapYeuCau",
      render: (field, item, index) => {
        return `${soLuongSoCap(item.soLuongYeuCau, item.heSoDinhMuc)} ${
          item.tenDvtSoCap || ""
        }`;
      },
    },
    {
      title: (
        <HeaderSearch title={t("kho.sLSoCapDuyet")} sort_key="soLuongSoCap" />
      ),
      key: "soLuongSoCap",
      width: 83,
      dataIndex: "soLuongSoCap",
      hideSearch: true,
      align: "right",
      show: true,
      i18Name: "kho.sLSoCapDuyet",
      render: (field, item, index) => {
        return `${roundToDigits(field, 3)} ${item.tenDvtSoCap || ""}`;
      },
    },
    {
      title: <HeaderSearch title={t("kho.slTonThucTeSoCap")} />,
      key: "soLuongTonDoiUng",
      width: 83,
      dataIndex: "soLuongTonDoiUng",
      hideSearch: true,
      align: "right",
      show: true,
      i18Name: "kho.slTonThucTeSoCap",
      hidden:
        (thongTinPhieu?.loaiNhapXuat !== LOAI_NHAP_XUAT.LINH_NOI_TRU &&
          thongTinPhieu?.loaiNhapXuat !== LOAI_NHAP_XUAT.LINH_BU_TU_TRUC) ||
        !window.location.pathname.includes("/kho/xuat-kho/chi-tiet-linh-bu/"),
      render: (item, data) => {
        let soLuongTon = (item || 0) / (data.heSoDinhMuc || 1);
        return `${item ? roundToDigits(soLuongTon, 3) : ""} ${
          data.tenDvtSoCap ? data.tenDvtSoCap : ""
        }`;
      },
    },
    {
      title: <HeaderSearch title={t("kho.soLo")} sort_key="soLo" />,
      width: 150,
      dataIndex: "loNhap",
      key: "soLo",
      hideSearch: true,
      align: "center",
      show: true,
      i18Name: "kho.soLo",
      hidden: dataAN_THONG_TIN_HSD_VA_SO_LO?.eval(),
      render: (item, record) => {
        const value = record?.loNhapId;
        if (isEditSoLo) {
          return (
            <DropdownSoLo
              fallbackValue={record?.soLo || record?.loNhap?.soLo}
              value={value}
              onChange={(e) => {
                const _record = getPhieuNhapXuat(record.id);
                _record.loNhapId = e;
                updateData({ dsNhapXuatChiTiet: [...dsNhapXuatChiTiet] });
              }}
              fetchData={() =>
                khoTonKhoProvider.theoLo({
                  khoId: thongTinPhieu?.khoDoiUngId,
                  ma: record?.ma,
                  dsLoaiDichVu: record?.loaiDichVu && [record?.loaiDichVu],
                  theoSoLuongTonKho: THEO_SO_LUONG_TON_KHO.CON_TON_KHA_DUNG,
                  size: 500,
                })
              }
            />
          );
        }
        return item?.soLo || item?.loNhap?.soLo;
      },
    },
    {
      title: <HeaderSearch title={t("kho.hanSuDung")} sort_key="hanSuDung" />,
      width: 150,
      dataIndex: "loNhap",
      key: "hanSuDung",
      hideSearch: true,
      show: true,
      i18Name: "kho.hanSuDung",
      hidden: dataAN_THONG_TIN_HSD_VA_SO_LO?.eval(),
      render: (value, _, __) =>
        value?.ngayHanSuDung
          ? moment(value?.ngayHanSuDung)?.format("DD/MM/YYYY")
          : "", // todo
    },
    {
      title: <HeaderSearch title={t("danhMuc.giaBaoHiem")} />,
      width: 100,
      dataIndex: "giaBaoHiem",
      key: "giaBaoHiem",
      i18Name: "danhMuc.giaBaoHiem",
      hideSearch: true,
      show: true,
      align: "right",
      render: (item, record) =>
        record?.loNhap?.giaBaoHiem && record?.loNhap?.giaBaoHiem.formatPrice(),
    },
    {
      title: <HeaderSearch title={t("danhMuc.giaKhongBaoHiem")} />,
      width: 100,
      dataIndex: "giaKhongBaoHiem",
      key: "giaKhongBaoHiem",
      i18Name: "danhMuc.giaKhongBaoHiem",
      hideSearch: true,
      show: true,
      align: "right",
      render: (item, record) =>
        record?.loNhap?.giaKhongBaoHiem &&
        record?.loNhap?.giaKhongBaoHiem.formatPrice(),
    },
    {
      title: <HeaderSearch title={t("common.hamLuong")} />,
      dataIndex: "hamLuong",
      key: "hamLuong",
      i18Name: "common.hamLuong",
      width: 80,
      show: true,
      hidden: ![LOAI_NHAP_XUAT.LINH_BU_TU_TRUC].includes(
        thongTinPhieu?.loaiNhapXuat
      ),
      align: "right",
      render: (item, record) => record?.dichVu?.hamLuong,
    },
    {
      title: <HeaderSearch title={t("kho.coSoTrenKhoNhap")} />,
      dataIndex: "coSoSoCap",
      key: "coSoSoCap",
      i18Name: "kho.coSoTrenKhoNhap",
      width: 80,
      align: "right",
      show: true,
      hidden: ![LOAI_NHAP_XUAT.DU_TRU, LOAI_NHAP_XUAT.LINH_BU_TU_TRUC].includes(
        thongTinPhieu?.loaiNhapXuat
      ),
    },
    {
      title: <HeaderSearch title={t("kho.coSoDuoiKhoNhap")} />,
      dataIndex: "coSoDuoiSoCap",
      key: "coSoDuoiSoCap",
      i18Name: "kho.coSoDuoiKhoNhap",
      width: 80,
      align: "right",
      show: true,
      hidden: ![LOAI_NHAP_XUAT.DU_TRU, LOAI_NHAP_XUAT.LINH_BU_TU_TRUC].includes(
        thongTinPhieu?.loaiNhapXuat
      ),
    },
    {
      title: <HeaderSearch title={t("kho.quyCach")} />,
      dataIndex: "quyCach",
      key: "quyCach",
      i18Name: "kho.quyCach",
      width: 120,
      align: "right",
      show: true,
      render: (_, data) => data?.dichVu?.quyCach,
    },
    {
      title: <HeaderSearch title={t("common.tienIch")} />,
      key: "",
      width: 83,
      dataIndex: "",
      hideSearch: true,
      hidden: !isEdit,
      align: "right",
      render: (_, item, index) => {
        return (
          <SVG.IcDelete onClick={onDelete(item, index)} className="ic-action" />
        );
      },
    },
  ];

  return (
    <Main className="main">
      <TableWrapper
        scroll={{ y: 453, x: "auto" }}
        rowKey={(item) => item.id}
        columns={columns}
        dataSource={dsNhapXuatChiTietFilterMemo}
        locale={{
          emptyText: (
            <TableEmpty
              onClickButton={onFocusSearchHangHoa}
              showButton={isEdit}
            />
          ),
        }}
        ref={ref}
        onRow={onRow}
        tableName="TABLE_KHO_PHIEULINH_DSHangHoa"
        rowClassName={(record) =>
          record.id == selectedId ? "row-selected-detail" : ""
        }
      />
    </Main>
  );
};

export default memo(forwardRef(DanhSachHangHoa));
