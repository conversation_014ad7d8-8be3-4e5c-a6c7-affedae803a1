import React, {
  forwardRef,
  useEffect,
  useImperative<PERSON>andle,
  useMemo,
  useState,
  useRef,
} from "react";
import { Main } from "./styled";
import {
  NumberFormat,
  Checkbox,
  HeaderSearch,
  TableWrapper,
  Select,
} from "components";
import TableEmpty from "pages/kho/components/TableEmpty";
import { useDispatch } from "react-redux";
import {
  formatDecimal,
  formatNumber,
  isArray,
  isNumber,
  roundToDigits,
} from "utils/index";
import { Input, InputNumber } from "antd";
import {
  LOAI_DICH_VU,
  LOAI_NHAP_XUAT,
  ROLES,
  THEO_SO_LUONG_TON_KHO,
  THIET_LAP_CHUNG,
} from "constants/index";
import { useListAll, useQueryString, useStore, useThietLap } from "hooks";
import { SVG } from "assets";
import { cloneDeep, isEqual } from "lodash";
import { t } from "i18next";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import { useLocation } from "react-router-dom";
import { checkRole } from "lib-utils/role-utils";
import { TRANG_THAI_PHIEU_NUMBER } from "pages/quanLyBaoCaoAdr/config";
import DropdownSoLo from "pages/nhaThuoc/ChiTietDonThuoc/LeftPanel/DanhSach/DropdownSoLo";
import khoTonKhoProvider from "data-access/kho/kho-ton-kho-provider";
import { customSortBySttAndName } from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuLinh/utils";
import { getTenHangHoa, groupByMaThuoc } from "utils/kho-utils";

const blockInvalidChar = (e) => {
  if (
    (e.keyCode >= 48 && e.keyCode <= 57) ||
    (e.keyCode >= 96 && e.keyCode <= 105) || // các số trên bàn phím
    e.keyCode === 9 || // tab
    e.which === 8 || // dấu -
    e.keyCode === 37 || // mũi tên trái
    e.keyCode === 39 || // mũi tên phải
    e.keyCode === 190 || // dấu .
    e.keyCode === 188 // dấu ,
  ) {
  } else {
    return e.preventDefault();
  }
};

const DanhSachHangHoa = (
  { onFocusSearchHangHoa, isEdit, trangThai, validator, parentState },
  ref
) => {
  const [state, setState] = useState({
    dataRender: [],
    data: [],
  });
  const [loading, setLoading] = useState(false);
  const [type] = useQueryString("type", "");
  const [dichVuId] = useQueryString("dichVuId", "");
  const location = useLocation();
  const refSettings = useRef(null);
  const listHinhThucNhapXuat = useStore("hinhThucNhapXuat.listTongHop", []);
  let { thongTinPhieu, dsNhapXuatChiTiet } = useStore(
    "phieuNhapXuat",
    {},
    { fields: "thongTinPhieu, dsNhapXuatChiTiet" }
  );
  const listDvVacxin = useStore("chiDinhDichVuKho.listDvVacxin");
  const [listAllNguonSuDung] = useListAll("nguonSuDung");
  const {
    phieuNhapXuat: { updateData, onSelectMultiItem },
    chiDinhDichVuKho: { getListDichVuVacxin },
    tonKho: {
      onSearch: onSearchTonKho,
      onChangeInputSearch,
      updateData: updateDataTonKho,
    },
  } = useDispatch();
  const [dataMA_LOAI_XUAT_HUY] = useThietLap(THIET_LAP_CHUNG.MA_LOAI_XUAT_HUY);
  const listMaLoaiXuatHuy = dataMA_LOAI_XUAT_HUY.split(",");
  const [dataGOP_DONG_THUOC, finishGOP_DONG_THUOC] = useThietLap(
    THIET_LAP_CHUNG.GOP_DONG_THUOC
  );
  const [dataNHAP_KICH_CO_VAT_TU] = useThietLap(
    THIET_LAP_CHUNG.NHAP_KICH_CO_VAT_TU,
    "0"
  );
  const [dataTRU_TON_THEO_NGUON_SU_DUNG] = useThietLap(
    THIET_LAP_CHUNG.TRU_TON_THEO_NGUON_SU_DUNG
  );

  const showKichCo =
    dataNHAP_KICH_CO_VAT_TU == "1" || dataNHAP_KICH_CO_VAT_TU == "2";

  useEffect(() => {
    if (
      thongTinPhieu?.loaiNhapXuat === LOAI_NHAP_XUAT.XUAT_VAC_XIN_TIEM &&
      thongTinPhieu.id
    ) {
      getListDichVuVacxin({
        nbDotDieuTriId: thongTinPhieu.nbDotDieuTriId,
        phieuTiemChungId: thongTinPhieu.id,
      });
    }
  }, [thongTinPhieu]);

  useEffect(() => {
    window.history.replaceState({}, "");
    return () => {
      updateDataTonKho({ dataSearch: {} });
    };
  }, []);

  useEffect(() => {
    if (dichVuId) {
      setLoading(true);
      const { page, size, ten, ma, sort, from, khoId } = getAllQueryString();
      const updateDataSource = (data) => {
        let dataSource =
          dichVuId == "true"
            ? data
            : (data || []).filter((item) =>
                dichVuId.split(",").map(Number).includes(item.dichVuId)
              );
        if (isArray(dataSource, true)) {
          dataSource = dataSource.map((item, index) => {
            const coSoHienTai = Math.abs(
              (item.soLuongSoCapKhaDung || 0) - (item.coSoSoCap || 0)
            );
            return {
              ...item,
              index: dataSource.length + 1 + index,
              vatTuTaiSuDung: false,
              moi: true,
              id: null,
              soLuongSoCapYeuCau: coSoHienTai,
              soLuongSoCap: coSoHienTai,
              soLuongYeuCau: coSoHienTai * (item.heSoDinhMuc || 1),
              soLuong: coSoHienTai * (item.heSoDinhMuc || 1),
            };
          });
          setState({ dataSource });
          updateData({ dsNhapXuatChiTiet: dataSource });
        }
      };

      onSearchTonKho({
        dataSearch: {
          ma: ma || "",
          ten: ten || "",
          vuotCoSo: true,
          khoId: Number(khoId),
        },
        page: parseInt(page || 0),
        size: parseInt(
          dichVuId == "true" || from === "noiTru" ? 9999 : size || 10
        ),
        fromTongHop: true,
        ...(sort && { dataSortColumn: JSON.parse(sort) }),
      })
        .then((res) => {
          updateDataSource(res);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [dichVuId, thongTinPhieu?.khoId, thongTinPhieu?.loaiNhapXuat]);

  useEffect(() => {
    if (
      thongTinPhieu?.loaiNhapXuat == "45" &&
      !dsNhapXuatChiTiet?.length &&
      location?.state?.isXuatDaoHanSuDung
    ) {
      onChangeInputSearch({
        fromTongHop: true,
        khoId: thongTinPhieu?.khoId,
        theoSoLuongTonKho: THEO_SO_LUONG_TON_KHO.CON_TON_KHA_DUNG,
        size: 300,
      }).then((s) => {
        s.forEach((item) => {
          if (item.vatTuTaiSuDung) {
            item.soLuongYeuCau = item.soLuongSoCapKhaDung;
          } else {
            item.soLuongSoCapYeuCau = item.soLuongSoCapKhaDung;
          }
        });
        onSelectMultiItem({ data: s });
      });
    }
  }, [thongTinPhieu, location, dsNhapXuatChiTiet]);

  const onChange = (type, item, index) => (e) => {
    if (
      isEdit &&
      thongTinPhieu?.trangThai &&
      ![10, 15, 20].includes(thongTinPhieu?.trangThai)
    )
      return;
    let value = e?.target
      ? e.target.hasOwnProperty("checked")
        ? e?.target.checked
        : e?.target.value
      : e;

    if (
      (type === "soLuongSoCapYeuCau" &&
        thongTinPhieu?.loaiNhapXuat !== LOAI_NHAP_XUAT.XUAT_DAO_HAN_SU_DUNG) ||
      (type === "soLuongSoCap" &&
        thongTinPhieu?.loaiNhapXuat !== LOAI_NHAP_XUAT.XUAT_DAO_HAN_SU_DUNG) ||
      type === "soLuongYeuCau" ||
      type === "soLuong"
    ) {
      value = e.floatValue;
    }
    let ds = dsNhapXuatChiTiet || [];
    const newItem = { ...ds[index], [type]: value };
    if (type === "soLuongSoCapYeuCau") {
      newItem.soLuongSoCap = value;
      newItem.soLuong = null;
      newItem.soLuongYeuCau = null;
    }

    if (type === "soLuongYeuCau") {
      newItem.soLuong = value;
      newItem.soLuongSoCapYeuCau = null;
      newItem.soLuongSoCap = null;
    }
    if (type === "loNhapId") {
      newItem.loNhapId = value;
    }
    if (type === "vatTuTaiSuDung") {
      newItem.moi = !value;
    }

    ds[index] = newItem;
    setState({ dataSource: [...ds] });
    updateData({ dsNhapXuatChiTiet: [...ds] });
  };

  useImperativeHandle(ref, () => ({
    updateData: (data) =>
      setState({
        data,
        dataRender: data,
      }),
  }));

  const checkLoaiNhapXuat = [
    LOAI_NHAP_XUAT.DU_TRU,
    LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO,
    LOAI_NHAP_XUAT.XUAT_TRA_NHA_CUNG_CAP,
    LOAI_NHAP_XUAT.XUAT_KHAC,
  ].includes(thongTinPhieu.loaiNhapXuat);

  const handleAddNewLine = (dataSource) => {
    let data = cloneDeep(dataSource);
    //thêm điều kiện có line mới thì mới xử lý focus
    if (data?.length && data?.length !== state.dataSource?.length) {
      let item = data?.[data.length - 1]?.id
        ? data?.[data.length - 1]?.id
        : data?.[data.length - 1]?.detachId;
      setTimeout(() => {
        document.getElementById(item)?.focus();
      }, 300);
    }
    setState({ dataSource: data });
  };

  useEffect(() => {
    handleAddNewLine(dsNhapXuatChiTiet);
  }, [dsNhapXuatChiTiet]);

  useEffect(() => {
    if (
      isEdit &&
      dsNhapXuatChiTiet?.length &&
      parentState.dieuChinhCoSoDuoi &&
      thongTinPhieu?.khoDoiUngId
    ) {
      Promise.all(
        dsNhapXuatChiTiet.map((itemNhapXuat, indexNhapXuat) => {
          if (
            itemNhapXuat.moi &&
            !isNumber(itemNhapXuat.soLuongCoSoDuoiSoCap)
          ) {
            return onChangeInputSearch({
              size: 10,
              fromTongHop: true,
              theoLo: parentState.xuatTheoLo === true ? true : false,
              updateRedux: false,
              ten: itemNhapXuat.ten,
              khoId: thongTinPhieu?.khoDoiUngId,
              ma: itemNhapXuat.ma,
              ...(thongTinPhieu.loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_TRA_TAI_KHOA
                ? {
                    dsKhoTaiKhoaId: [thongTinPhieu.khoaChiDinhId],
                  }
                : {}),
              taiKhoa:
                thongTinPhieu?.loaiNhapXuat === LOAI_NHAP_XUAT.XUAT_TRA_TAI_KHOA
                  ? true
                  : false,
              theoSoLuongTonKho: listMaLoaiXuatHuy.includes(
                listHinhThucNhapXuat.find(
                  (item) => item.id === thongTinPhieu.hinhThucNhapXuatId
                )?.ma
              )
                ? THEO_SO_LUONG_TON_KHO.CON_TON
                : THEO_SO_LUONG_TON_KHO.CON_TON_KHA_DUNG,
            }).then((response) => {
              if (response.length === 1) {
                itemNhapXuat.soLuongCoSoDuoiSoCap = response[0].coSoDuoiSoCap;
              }
              return itemNhapXuat;
            });
          } else {
            return Promise.resolve(itemNhapXuat);
          }
        })
      ).then((response) => {
        handleAddNewLine(response);
      });
    }
  }, [dsNhapXuatChiTiet?.length, parentState, thongTinPhieu, isEdit]);

  const decimalScale = useMemo(() => {
    let result = 2;
    if (
      [LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO, LOAI_NHAP_XUAT.XUAT_KHAC].includes(
        thongTinPhieu?.loaiNhapXuat
      ) &&
      isEdit
    ) {
      result = 3;
    }
    return result;
  }, [thongTinPhieu, isEdit]);

  const canEditSlDuyet = useMemo(() => {
    let result = false;
    if (thongTinPhieu.loaiNhapXuat === LOAI_NHAP_XUAT.DU_TRU) {
      result =
        window.location.pathname.indexOf("/chinh-sua") > -1 &&
        thongTinPhieu.trangThai === 20;

      if (checkRole([ROLES["KHO"].KHONG_DUOC_SUA_SL_DUYET_PHIEU_XUAT_DU_TRU])) {
        result = false;
      }
    }
    return result;
  }, [thongTinPhieu]);

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      key: "stt",
      width: 60,
      align: "center",
      ignore: true,
      render: (_, __, index) => index + 1,
    },
    {
      title: <HeaderSearch title={t("kho.maHangHoa")} />,
      key: "ma",
      dataIndex: "ma",
      i18Name: "kho.maHangHoa",
      width: 90,
      show: true,
    },
    {
      title: <HeaderSearch title={t("kho.tenHangHoa")} />,
      key: "ten",
      width: 250,
      i18Name: "kho.tenHangHoa",
      show: true,
      render: (_, data) => {
        return getTenHangHoa(data, dataNHAP_KICH_CO_VAT_TU);
      },
    },
    {
      title: <HeaderSearch title={t("kho.hoatChat")} />,
      key: "dichVu",
      width: 200,
      i18Name: "kho.hoatChat",
      show: true,
      hidden: ![
        LOAI_NHAP_XUAT.DU_TRU,
        LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO,
        LOAI_NHAP_XUAT.XUAT_TRA_NHA_CUNG_CAP,
        LOAI_NHAP_XUAT.XUAT_KHAC,
      ].includes(thongTinPhieu.loaiNhapXuat),
      render: (_, data) => data?.tenHoatChat,
    },
    {
      title: <HeaderSearch title={t("kho.dvt")} />,
      align: "center",
      key: "tenDvtSoCap",
      width: 100,
      i18Name: "kho.dvt",
      show: true,
      render: (_, data) => data?.tenDvtSoCap,
    },
    ...(thongTinPhieu?.dsLoaiDichVu?.includes(LOAI_DICH_VU.VAT_TU) &&
    type === "90"
      ? [
          {
            title: <HeaderSearch title={t("kho.xuatSlTaiSuDung")} />,
            align: "center",
            key: "vatTuTaiSuDung",
            dataIndex: "vatTuTaiSuDung",
            width: "150px",
            i18Name: "kho.xuatSlTaiSuDung",
            render: (item, data, index) => (
              <Checkbox
                checked={item}
                onChange={onChange("vatTuTaiSuDung", data, index)}
              ></Checkbox>
            ),
          },
        ]
      : []),
    ...(thongTinPhieu?.dsLoaiDichVu?.includes(LOAI_DICH_VU.THUOC)
      ? [
          {
            title: <HeaderSearch title={t("kho.hamLuong")} />,
            key: "hamLuong",
            align: "right",
            width: "100px",
            i18Name: "kho.hamLuong",
            show: true,
            render: (_, data) => data?.hamLuong,
          },
        ]
      : []),
    {
      title: <HeaderSearch title={t("kho.slYeuCau")} />,
      key: "soLuongSoCapYeuCau",
      align: "right",
      width: 100,
      dataIndex: "soLuongSoCapYeuCau",
      i18Name: "kho.slYeuCau",
      show: true,
      render: (value, data, index) => {
        let key = data.vatTuTaiSuDung ? "soLuongYeuCau" : "soLuongSoCapYeuCau";
        let isError = validator ? validator.fieldValidate(key)([data]) : false;
        return isEdit ? (
          <>
            {thongTinPhieu?.loaiNhapXuat ===
            LOAI_NHAP_XUAT.XUAT_DAO_HAN_SU_DUNG ? (
              <Input
                id={data?.id}
                className="input-option"
                onChange={onChange(key, data, index)}
                value={data[key]}
                placeholder={t("common.nhapSoLuong")}
                onKeyDown={blockInvalidChar}
                style={{ textAlign: "right", paddingRight: "10px" }}
                autoFocus
              />
            ) : (
              <NumberFormat
                id={data?.id}
                customInput={Input}
                thousandSeparator="."
                decimalSeparator=","
                decimalScale={decimalScale}
                className="input-option"
                onValueChange={onChange(key, data, index)}
                value={data[key]}
                placeholder={t("common.nhapSoLuong")}
                onKeyDown={blockInvalidChar}
                style={{ textAlign: "right", paddingRight: "10px" }}
                autoFocus
              />
            )}
            {isError && (
              <div className="error">
                SL yêu cầu không được lớn hơn SL khả dụng
              </div>
            )}
          </>
        ) : value ? (
          formatDecimal(value)
        ) : (
          formatDecimal(data.soLuong)
        );
      },
    },
    {
      title: <HeaderSearch title={t("common.slDuyet")} />,
      key: "soLuongSoCap",
      align: "right",
      width: 80,
      dataIndex: "soLuongSoCap",
      i18Name: "common.slDuyet",
      show: true,
      render: (item, data, index) => {
        return canEditSlDuyet ? (
          <NumberFormat
            customInput={Input}
            thousandSeparator="."
            decimalSeparator=","
            decimalScale={decimalScale}
            className="input-option"
            onValueChange={onChange(
              `${data.vatTuTaiSuDung ? "soLuong" : "soLuongSoCap"}`,
              data,
              index
            )}
            value={data.vatTuTaiSuDung ? data.soLuong : data.soLuongSoCap}
            placeholder={t("common.nhapSoLuong")}
            onKeyDown={blockInvalidChar}
          />
        ) : item ? (
          formatDecimal(item)
        ) : (
          formatDecimal(data.soLuong)
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={`${
            thongTinPhieu.kho && checkLoaiNhapXuat
              ? `${t("kho.slKhaDung")} (${thongTinPhieu.kho?.ten})`
              : `${t("kho.slKhaDung")}`
          }`}
        />
      ),
      key: "soLuongKhaDung",
      dataIndex: "soLuongKhaDung",
      align: "right",
      width: 110,
      show: true,
      i18Name: `${
        thongTinPhieu.kho && checkLoaiNhapXuat
          ? `${t("kho.slKhaDung")} (${thongTinPhieu.kho?.ten})`
          : `${t("kho.slKhaDung")}`
      }`,
      render: (value, item) =>
        value &&
        formatDecimal(roundToDigits(value / (item.heSoDinhMuc || 1), 3)),
    },
    {
      title: (
        <HeaderSearch
          title={`${
            thongTinPhieu.khoDoiUng
              ? `${t("kho.slKhaDung")} (${thongTinPhieu.khoDoiUng?.ten})`
              : `${t("kho.slKhaDung")} (${t("kho.khoDoiUng")})`
          }`}
        />
      ),
      i18Name: `${
        thongTinPhieu.khoDoiUng
          ? `${t("kho.slKhaDung")} (${thongTinPhieu.khoDoiUng?.ten})`
          : `${t("kho.slKhaDung")} (${t("kho.khoDoiUng")})`
      }`,
      key: "soLuongKhaDungDoiUng",
      dataIndex: "soLuongKhaDungDoiUng",
      align: "right",
      width: 110,
      show: true,
      hidden: !checkLoaiNhapXuat,
      render: (value, item) =>
        value &&
        formatDecimal(roundToDigits(value / (item.heSoDinhMuc || 1), 3)),
    },
    {
      title: <HeaderSearch title={t("kho.soLo")} />,
      key: "soLo",
      align: "right",
      width: 100,
      i18Name: "kho.soLo",
      show: true,
      render: (_, item, index) => {
        if (
          isEdit &&
          checkRole([ROLES.KHO.CHINH_SUA_SO_LO_TRUOC_KHI_XUAT]) &&
          thongTinPhieu.trangThai === TRANG_THAI_PHIEU_NUMBER.CHO_DUYET
        ) {
          const value = item?.loNhapId;

          return (
            <DropdownSoLo
              fallbackValue={item?.soLo || item?.loNhap?.soLo}
              value={value}
              onChange={onChange("loNhapId", item, index)}
              showKichCo={showKichCo}
              fetchData={() =>
                khoTonKhoProvider.theoLo({
                  khoId: thongTinPhieu?.khoDoiUngId,
                  ma: item?.ma,
                  dsLoaiDichVu: item?.loaiDichVu && [item?.loaiDichVu],
                  theoSoLuongTonKho: THEO_SO_LUONG_TON_KHO.CON_TON_KHA_DUNG,
                  size: 500,
                })
              }
            />
          );
        }
        return item?.soLo || item?.loNhap?.soLo;
      },
    },
    {
      title: <HeaderSearch title={t("kho.quyetDinhThau.title")} />,
      key: "quyetDinhThau",
      dataIndex: "quyetDinhThau",
      align: "right",
      width: 100,
      i18Name: "kho.quyetDinhThau.title",
      show: true,
      hidden: !checkLoaiNhapXuat,
      render: (item, data) => data?.loNhap?.quyetDinhThau,
    },
    {
      title: <HeaderSearch title={t("kho.hanSuDung")} />,
      key: "hanSuDung",
      align: "right",
      i18Name: "kho.hanSuDung",
      show: true,
      width: 100,
      render: (_, data) =>
        (data?.ngayHanSuDung || data?.loNhap?.ngayHanSuDung) &&
        (data?.ngayHanSuDung || data?.loNhap?.ngayHanSuDung)
          .toDateObject()
          .format("dd/MM/yyyy"),
    },
    {
      title: <HeaderSearch title={t("kho.coSoTrenSauDuyet")} />,
      dataIndex: "coSoSoCap",
      key: "coSoSoCap",
      i18Name: "kho.coSoTrenSauDuyet",
      show: true,
      width: 80,
      align: "right",
      hidden: !parentState?.dieuChinhCoSo || thongTinPhieu?.trangThai === 30,
      render: (field, data) => {
        const slDuyet = data.soLuongSoCap || data.soLuong || 0;
        return isNumber(field) ? formatNumber((field || 0) + slDuyet) : "";
      },
    },
    {
      title: <HeaderSearch title={t("kho.coSoDuoiSauDuyet")} />,
      dataIndex: "soLuongCoSoDuoiSoCap",
      key: "soLuongCoSoDuoiSoCap",
      i18Name: "kho.coSoDuoiSauDuyet",
      show: true,
      width: 80,
      align: "right",
      hidden: !parentState?.dieuChinhCoSoDuoi,
      render: (field, data, index) => {
        return window.location.pathname.indexOf("/them-moi") >= 0 ||
          window.location.pathname.indexOf("/chinh-sua") >= 0 ? (
          <InputNumber
            min={0}
            placeholder={t("common.nhapSoLuong")}
            onChange={onChange("soLuongCoSoDuoiSoCap", data, index)}
            type="number"
            value={field}
          />
        ) : (
          field
        );
      },
    },
    {
      title: <HeaderSearch title={t("kho.coSoTrenKhoNhap")} />,
      dataIndex: "coSoSoCap",
      key: "coSoSoCap",
      i18Name: "kho.coSoTrenKhoNhap",
      width: 80,
      align: "right",
      show: true,
      hidden: ![LOAI_NHAP_XUAT.DU_TRU, LOAI_NHAP_XUAT.LINH_BU_TU_TRUC].includes(
        thongTinPhieu?.loaiNhapXuat
      ),
    },
    {
      title: <HeaderSearch title={t("kho.coSoDuoiKhoNhap")} />,
      dataIndex: "coSoDuoiSoCap",
      key: "coSoDuoiSoCap",
      i18Name: "kho.coSoDuoiKhoNhap",
      width: 80,
      align: "right",
      show: true,
      hidden: ![LOAI_NHAP_XUAT.DU_TRU, LOAI_NHAP_XUAT.LINH_BU_TU_TRUC].includes(
        thongTinPhieu?.loaiNhapXuat
      ),
    },
    {
      title: <HeaderSearch title={t("common.hamLuong")} />,
      dataIndex: "hamLuong",
      key: "hamLuong",
      i18Name: "common.hamLuong",
      width: 80,
      align: "right",
      show: true,
      hidden: ![LOAI_NHAP_XUAT.DU_TRU].includes(thongTinPhieu?.loaiNhapXuat),
    },
    {
      title: <HeaderSearch title={t("danhMuc.nguonSuDung")} />,
      key: "nguonSuDungKhoId ",
      align: "right",
      width: 100,
      i18Name: "danhMuc.nguonSuDung",
      show: true,
      hidden:
        !dataTRU_TON_THEO_NGUON_SU_DUNG?.eval() ||
        ![
          LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO,
          LOAI_NHAP_XUAT.XUAT_KHAC,
          LOAI_NHAP_XUAT.XUAT_TRA_NHA_CUNG_CAP,
        ].includes(thongTinPhieu?.loaiNhapXuat),
      render: (_, item, index) => {
        let value = item?.nguonSuDungKhoId ?? item?.loNhap?.nguonSuDungKhoId;
        if (isEdit && parentState?.xuatTheoNguonSd) {
          return (
            <Select
              data={listAllNguonSuDung}
              placeholder={t("danhMuc.chonNguonSuDung")}
              onChange={onChange("nguonSuDungKhoId", item, index)}
              value={value}
              dropdownMatchSelectWidth={300}
            />
          );
        }
        return item?.nguonSuDungKho?.ten ?? item?.loNhap?.nguonSuDungKho?.ten;
      },
    },
    {
      title: <HeaderSearch title={t("kho.quyCach")} />,
      dataIndex: "quyCach",
      key: "quyCach",
      i18Name: "kho.quyCach",
      width: 120,
      align: "right",
      show: true,
      render: (_, data) => data?.dichVu?.quyCach,
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.tienIch")}
              {!isEdit && (
                <SVG.IcSetting
                  onClick={onSettings}
                  style={{ cursor: "pointer" }}
                />
              )}
            </>
          }
        />
      ),
      key: "delete",
      width: 100,
      ignore: true,
      fixed: "right",
      align: "center",
      render: (_, data, index) => {
        let keyRemove = data.id ? "id" : "key";
        return (
          <>
            {!(
              !isEdit ||
              (thongTinPhieu?.trangThai &&
                ![10, 15].includes(thongTinPhieu?.trangThai))
            ) && (
              <SVG.IcDelete
                onClick={() => {
                  updateData({
                    dsNhapXuatChiTiet: dsNhapXuatChiTiet.filter(
                      (item) => item[keyRemove] !== data[keyRemove]
                    ),
                  });
                }}
                className="ic-action"
              />
            )}
          </>
        );
      },
    },
  ];

  const dataSource = useMemo(() => {
    let result = state.dataSource || [];
    if (thongTinPhieu?.loaiNhapXuat === LOAI_NHAP_XUAT.XUAT_VAC_XIN_TIEM) {
      result = listDvVacxin;
    }
    if (!isEdit && thongTinPhieu?.loaiNhapXuat === LOAI_NHAP_XUAT.DU_TRU) {
      result = customSortBySttAndName(state.dataSource, [
        "sttPhieuLinh",
        "ten",
      ]);
    }
    if (
      !isEdit &&
      finishGOP_DONG_THUOC &&
      dataGOP_DONG_THUOC?.eval() &&
      thongTinPhieu?.loaiNhapXuat === LOAI_NHAP_XUAT.DU_TRU
    ) {
      result = groupByMaThuoc(cloneDeep(result), "ma");
    }
    return result;
  }, [
    thongTinPhieu,
    listDvVacxin,
    state.dataSource,
    isEdit,
    finishGOP_DONG_THUOC,
    dataGOP_DONG_THUOC,
  ]);

  return (
    <Main>
      <TableWrapper
        locale={{
          emptyText: (
            <TableEmpty
              showButton={isEdit}
              onClickButton={isEdit && onFocusSearchHangHoa}
            />
          ),
        }}
        loading={!!loading}
        showSorterTooltip={false}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        rowKey={(record) => record.index}
        tableName={`table_KHO_ChiTietPhieuXuat_${
          isEdit ? "ChinhSua" : "Chitiet"
        }_${thongTinPhieu?.loaiNhapXuat}`}
        ref={refSettings}
        alwayGetFromCache={true}
        columnResizable={true}
      />
    </Main>
  );
};

export default forwardRef(DanhSachHangHoa);
