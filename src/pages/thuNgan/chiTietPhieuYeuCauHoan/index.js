import React, { useEffect, useMemo, useRef, useState } from "react";
import { MainPage, InputSearch } from "./styled";
import ThongTinPhieu from "./thongTinPhieu";
import BangThongTin from "./bangThongTin";
import { Col, Input, Row, message, Dropdown, Menu } from "antd";
import ThongTinSoTien from "./thongTinSoTien";
import { useDispatch } from "react-redux";
import printProvider, { printJS } from "data-access/print-provider";
import {
  Toolt<PERSON>,
  AuthWrapper,
  Button,
  ModalSignPrint,
  ChonQuay,
  ChonCaLamViec,
  ChonKho,
  BarcodeScannerInput,
} from "components";
import { useTranslation } from "react-i18next";
import { checkRole, checkRoleOr } from "lib-utils/role-utils";
import {
  ROLES,
  CACHE_KEY,
  LOAI_QUAY,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
  THIET_LAP_CHUNG,
  MA_BIEU_MAU_EDITOR,
} from "constants/index";
import {
  useStore,
  useConfirm,
  useLoading,
  useThietLap,
  useListAll,
} from "hooks";
import { useHistory, useParams, useLocation } from "react-router-dom";
import ThongTinBenhNhan from "../chiTietPhieuThu/thongTinBenhNhan";
import ModalChuyenPhieuHoan from "./ModalChuyenPhieuHoan";
import { SVG } from "assets";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { refConfirm } from "app";
import { isBoolean, openInNewTab } from "utils/index";
import ModalNhapLyDo from "pages/kho/components/ModalNhapLyDo";
import ModalChonPTTT from "./ModalSuaPTTT";
import ModalInPhieuTomTatBenhAn from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuTomTatBenhAn";
import ModalHoanTraThuoc from "./ModalHoanTraThuoc";
import { isArray } from "lodash";

const { useCaLamViec } = ChonCaLamViec;

const sleep = (ms) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

const ChiTietPhieuYeuCauHoan = (props) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const [caLamViec] = useCaLamViec();

  const refModalChuyenPhieuHoan = useRef(null);
  const refModalSignPrint = useRef(null);
  const refNhapLyDo = useRef(null);
  const refChonPhuongThuc = useRef(null);
  const refModalInPhieuTomTatBA = useRef(null);
  const refModalHoanTraThuoc = useRef(null);

  const [state, _setState] = useState({
    showSearch: false,
    nhaTamUng: null,
    phieuChiItem: {},
    valueSearch: "",
    loadingPhieu: false,
    openPhieu: false,
  });
  const { state: locationState } = useLocation();
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const history = useHistory();
  const { showLoading, hideLoading } = useLoading();
  let timer = null;
  const {
    danhSachPhieuYeuCauHoan: {
      getChiTietPhieuDoiTra,
      confirmPhieuYeuCauHoan,
      onSearchChiTietPhieuHoan,
      phieuChi,
      chiPhieuThu,
    },
    thuNgan: { getThongTinPhieuThu, kiemTraNb15ThangLuong },
    danhSachPhieuThu: { getDsPhieuThu },
    dsHoaDonDienTu: { inHoaDon },
    phieuIn: { getListPhieu, showFileEditor, getFilePhieuIn },
    nbDvHoan: { hoanTienNganHang, huyGuiDuyetTraThuoc, huyYeuCauTraThuoc },
    khoa: { getKhoaTheoTaiKhoan },
  } = useDispatch();

  const chiTietPhieuDoiTra = useStore(
    "danhSachPhieuYeuCauHoan.chiTietPhieuDoiTra",
    {}
  );
  const thongTinBenhNhanTongHop = useStore(
    "nbDotDieuTri.thongTinBenhNhanTongHop",
    {}
  );
  const thongTinPhieuThu = useStore("thuNgan.thongTinPhieuThu", {});
  const { phieuHoanTraId, maHoSo, nbDotDieuTriId } = useParams();
  const [PHUONG_THUC_HOAN_TIEN_MAC_DINH, loadFinish] = useThietLap(
    THIET_LAP_CHUNG.PHUONG_THUC_HOAN_TIEN_MAC_DINH
  );
  const [listAllPhuongThucThanhToan] = useListAll("phuongThucTT");

  const [dataNGOAI_TRU_NHIEU_PHIEU_THU_BH] = useThietLap(
    THIET_LAP_CHUNG.NGOAI_TRU_NHIEU_PHIEU_THU_BH,
    "false"
  );
  const [dataNGOAI_TRU_HOAN_DICH_VU_BH_KHI_PT_DA_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.NGOAI_TRU_HOAN_DICH_VU_BH_KHI_PT_DA_THANH_TOAN,
    "false"
  );

  const [dataDINH_DANG_XEM_HOA_DON] = useThietLap(
    THIET_LAP_CHUNG.DINH_DANG_XEM_HOA_DON
  );

  const [dataMA_DOI_TAC_HOAN_TIEN] = useThietLap(
    THIET_LAP_CHUNG.MA_DOI_TAC_HOAN_TIEN,
    null
  );
  const { getKho } = ChonKho.useChonKho(CACHE_KEY.CHON_KHO_THU_NGAN);

  const getDanhSachPhieu = () => {
    if (
      state.loadingPhieu ||
      !nbDotDieuTriId ||
      state.openPhieu ||
      !chiTietPhieuDoiTra?.id
    ) {
      return;
    }
    setState({ loadingPhieu: true });
    getListPhieu({
      nbDotDieuTriId: nbDotDieuTriId,
      maManHinh: "004",
      maViTri: "00402",
    })
      .then((listPhieu) => {
        let _listPhieu = listPhieu.filter((x) => {
          if (chiTietPhieuDoiTra.loai === 40) {
            return x.ma !== "P879"; // Biên bản điều chỉnh hóa đơn nhà thuốc
          } else {
            return x.ma !== "P1287"; // Biên bản điều chỉnh hóa đơn trong viện
          }
        });
        setState({
          listPhieu: _listPhieu,
        });
      })
      .finally(() => {
        setState({ loadingPhieu: false });
      });
  };

  useEffect(() => {
    if (loadFinish) {
      if (chiTietPhieuDoiTra?.trangThai == 20) {
        setState({
          phuongThucTtId: listAllPhuongThucThanhToan.find(
            (x) => x.ma == PHUONG_THUC_HOAN_TIEN_MAC_DINH
          )?.id,
        });
      } else {
        setState({ phuongThucTtId: chiTietPhieuDoiTra.phuongThucTtId });
      }
    }
  }, [
    PHUONG_THUC_HOAN_TIEN_MAC_DINH,
    loadFinish,
    chiTietPhieuDoiTra,
    listAllPhuongThucThanhToan,
  ]);
  useEffect(() => {
    getChiTietPhieuDoiTra({ id: phieuHoanTraId }).then((res) => {
      if (res.dsDichVuHoan) {
        getThongTinPhieuThu(res.dsDichVuHoan[0]?.phieuThuId);
      }
    });
  }, [phieuHoanTraId]);

  useEffect(() => {
    getKhoaTheoTaiKhoan({ page: "", size: "", active: true });
  }, []);

  const handleSubmit = () => {
    if (!state.nhaTamUng) {
      message.error(t("baoCao.vuiLongChonNhaThuNgan"));
      return;
    }

    if (
      (checkRole([ROLES["THU_NGAN"].XAC_NHAN_HOAN_DICH_VU]) &&
        !thongTinPhieuThu.phatHanhHoaDon) ||
      (checkRole([ROLES["THU_NGAN"].XAC_NHAN_HOAN_DICH_VU_DA_XUAT_HDDT]) &&
        thongTinPhieuThu.phatHanhHoaDon) ||
      (checkRole([ROLES["THU_NGAN"].CHI_HOAN_NHA_THUOC]) &&
        chiTietPhieuDoiTra.loai === 40)
    ) {
      setState({ isLoading: true });
      //tạo ra 1 biến để gom tất cả các phiếu in lại
      let pdfFiles = [];
      const phuongThuc = (listAllPhuongThucThanhToan || []).find(
        (x) => x.id == state.phuongThucTtId
      );
      const confirmHoan = (data, duyetHoanKhacNha) => {
        const _phuongThucTtId = data?.phuongThucTtId || state.phuongThucTtId;
        const _phuongThuc = (listAllPhuongThucThanhToan || []).find(
          (x) => x.id == _phuongThucTtId
        );

        confirmPhieuYeuCauHoan({
          id: phieuHoanTraId,
          nhaThuNganId: state.nhaTamUng?.toaNhaId,
          caLamViecId: caLamViec?.id,
          phuongThucTtId: _phuongThucTtId,
          quayId: state.nhaTamUng?.id,
          ...(isBoolean(duyetHoanKhacNha) && { duyetHoanKhacNha }),
          ...(_phuongThuc?.tienMat
            ? {}
            : {
                nganHangId: data?.nganHangId,
                maChuanChi: data?.maChuanChi,
                soTaiKhoan: data?.soTaiKhoan,
                tenTaiKhoan: data?.tenTaiKhoan,
                nganHangNbId: data?.nganHangNbId,
              }),
        })
          .then(async (_res) => {
            getChiTietPhieuDoiTra({ id: chiTietPhieuDoiTra.id });
            await phieuChi(phieuHoanTraId).then(async (s) => {
              if (s.file.pdf) {
                pdfFiles.push(s.file.pdf);
                // printProvider.printMergePdf([s.file.pdf]);
              }
              const response = await kiemTraNb15ThangLuong({
                nbDotDieuTriId: nbDotDieuTriId,
              });
              if (response?.data) {
                refConfirm.current &&
                  refConfirm.current.show(
                    {
                      title: t("common.canhBao"),
                      content: t(
                        dataNGOAI_TRU_NHIEU_PHIEU_THU_BH?.eval()
                          ? "thuNgan.nguoiBenhCanSinhPhieuChiThanhToanLaiChoNb"
                          : "thuNgan.sinhPhieuChiChoPhieuThuDaThanhToanVaThucHienThanhToanLai"
                      ),
                      cancelText: t("common.quayLai"),
                      okText:
                        dataNGOAI_TRU_NHIEU_PHIEU_THU_BH?.eval() ||
                        dataNGOAI_TRU_HOAN_DICH_VU_BH_KHI_PT_DA_THANH_TOAN?.eval()
                          ? t("thuNgan.sinhPhieuChi")
                          : t("common.dongY"),
                      classNameOkText: "button-error",
                      showBtnOk: true,
                      typeModal: "warning",
                    },
                    async () => {
                      const s = await getDsPhieuThu({
                        nbDotDieuTriId: nbDotDieuTriId,
                        nhoHonMucCungChiTra: false,
                        thanhToan:
                          TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN,
                      });
                      if (
                        dataNGOAI_TRU_NHIEU_PHIEU_THU_BH?.eval() ||
                        dataNGOAI_TRU_HOAN_DICH_VU_BH_KHI_PT_DA_THANH_TOAN?.eval()
                      ) {
                        refNhapLyDo.current &&
                          refNhapLyDo.current.show(
                            {
                              title: t("thuNgan.lyDoSinhPhieuChi"),
                              message: t("thuNgan.dienLyDoSinhPhieuChi"),
                            },
                            async (lyDo) => {
                              await chiPhieuThu({
                                dsPhieuThuId: s?.map((o) => o.id),
                                lyDo,
                                nbDotDieuTriId,
                                nhaThuNganId: state.nhaTamUng?.toaNhaId,
                                quayId: state.nhaTamUng?.id,
                              });
                              openInNewTab(
                                `/thu-ngan/chi-tiet-phieu-thu/${s[0].maHoSo}/${s[0].id}/${nbDotDieuTriId}`
                              );
                            }
                          );
                      } else {
                        openInNewTab(
                          `/thu-ngan/chi-tiet-phieu-thu/${s[0].maHoSo}/${s[0].id}/${nbDotDieuTriId}`
                        );
                      }
                    },
                    () => {}
                  );
              }
            });
            await sleep(1500);
            if (_res.dsHoaDonId) {
              await inHoaDon({
                hoaDonId: _res.dsHoaDonId,
                dinhDang: dataDINH_DANG_XEM_HOA_DON,
              });
            }
          })
          .catch((error) => {
            let canhBaoKhacNha =
              error?.code === 1019 &&
              checkRole([ROLES["THU_NGAN"].DUOC_PHEP_HOAN_KHAC_NHA]);

            showConfirm(
              {
                title: t("common.canhBao"),
                content: canhBaoKhacNha
                  ? t(
                      "thuNgan.banCoChacChanMuonDuyetHoanChoPhieuThuKhongPhaiDoQuayCuaBanTaoRa"
                    )
                  : error?.message,
                cancelText: t("common.dong"),
                showBtnOk: canhBaoKhacNha,
                typeModal: "warning",
              },
              () => {
                confirmHoan(data, true);
              }
            );
          })
          .finally(() => {
            setState({ isLoading: false });
            if (pdfFiles.length > 0) {
              printProvider.printMergePdf(pdfFiles);
            }
          });
      };
      if (!phuongThuc?.tienMat) {
        refChonPhuongThuc.current &&
          refChonPhuongThuc.current.show(
            {
              phuongThucTtId: state.phuongThucTtId,
              nbDotDieuTriId,
              title: t("thuNgan.chonPhuongThucThanhToan"),
            },
            (data) => {
              confirmHoan(data);
            },
            () => {
              setState({ isLoading: false });
            }
          );
      } else {
        confirmHoan();
      }
    } else if (thongTinPhieuThu.phatHanhHoaDon) {
      showConfirm(
        {
          title: t("common.canhBao"),
          content: t("thuNgan.phieuYeuCauHoanDaXuatHoaDonDieuTu"),
          cancelText: t("common.troLai"),
          showBtnOk: false,
          typeModal: "warning",
        },
        () => {},
        () => {
          message.error(
            t(
              "thuNgan.taiKhoanKhongCoQuyenHoanChoPhieuYeuCauHoanDaXuatHoaDonDieuTu"
            )
          );
        }
      );
    } else {
      message.error(t("thuNgan.taiKhoanKhongCoQuyen"));
    }
  };

  const handleSearch = (e) => {
    clearTimeout(timer);
    timer = setTimeout(
      (s) => {
        if (s.value) {
          onSearchChiTietPhieuHoan({
            soPhieu: s.value,
          }).then((res) => {
            if (res.length) {
              history.push(
                `/thu-ngan/chi-tiet-phieu-hoan-tra/${res[0]?.maHoSo}/${res[0]?.id}/${res[0]?.nbDotDieuTriId}`
              );
            } else {
              message.error(`${t("thuNgan.khongTonTaiSoPhieu")} ${s.value}`);
            }
          });
        }
      },
      500,
      e.target
    );
  };

  const onSubmitSearch = (params) => {
    if (
      params.maHoSo ||
      params.maThe ||
      params.maBenhAn ||
      params.tenNb ||
      params.maNb
    ) {
      onSearchChiTietPhieuHoan({
        ...params,
      }).then((res) => {
        if (res.length) {
          history.push(
            `/thu-ngan/chi-tiet-phieu-hoan-tra/${res[0]?.maHoSo}/${res[0]?.id}/${res[0]?.nbDotDieuTriId}`
          );
        } else {
          message.error(
            t("thuNgan.khongTimThayThongTinPhieuYeuCauHoanDoiCuaNb")
          );
        }
      });
    } else {
      message.error(t("thuNgan.khongTimThayThongTinPhieuYeuCauHoanDoiCuaNb"));
    }
  };

  const handleClick = () => {
    history.push("/thu-ngan/ds-phieu-yeu-cau-hoan");
  };

  const onChangeCode = (e) => {
    setState({ valueSearch: e?.target?.value });
  };

  const onChuyenPhieuHoan = () => {
    refModalChuyenPhieuHoan.current && refModalChuyenPhieuHoan.current.show();
  };

  const onPrintPhieu = (item) => async () => {
    if (item.ma === "P540") {
      try {
        showLoading();
        await inHoaDon({
          hoaDonId: chiTietPhieuDoiTra?.dsHoaDonId,
          dinhDang: dataDINH_DANG_XEM_HOA_DON,
        });
      } catch (error) {
      } finally {
        hideLoading();
      }
    } else if (item.type == "editor") {
      let mhParams = {};
      //kiểm tra phiếu ký số
      if (checkIsPhieuKySo(item)) {
        //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
        mhParams = {
          nbDotDieuTriId: nbDotDieuTriId,
          maManHinh: "004",
          maViTri: "00402",
          kySo: true,
          maPhieuKy: item.ma,
        };
      }

      if (item.ma === "P712") {
        //Lọc bỏ phiếu có số phiếu = nbDotDieuTriId
        const _dsSoPhieu = (item.dsSoPhieu || []).filter(
          (x) =>
            x.soPhieu != nbDotDieuTriId && x.soPhieu && x.soPhieu !== "null"
        );
        refModalInPhieuTomTatBA.current &&
          refModalInPhieuTomTatBA.current.show(
            {
              dsSoPhieu: _dsSoPhieu,
              ten: item.ten,
              khoaChiDinhId: thongTinBenhNhanTongHop?.khoaNbId,
            },
            (data) => {
              const { thoiGianThucHien, khoaChiDinhId, id: idPhieu } = data;
              showFileEditor({
                phieu: item,
                id: idPhieu,
                nbDotDieuTriId,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                chiDinhTuDichVuId: nbDotDieuTriId,
                khoaChiDinhId,
                thoiGianThucHien,
                mhParams,
              });
            }
          );
      } else {
        if (item.ma === "P879" || item.ma === "P1287") {
          const dsHoaDon = isArray(chiTietPhieuDoiTra?.dsHoaDonGocId)
            ? chiTietPhieuDoiTra?.dsHoaDonGocId
            : [chiTietPhieuDoiTra?.dsHoaDonGocId];
          dsHoaDon
            .filter((item) => item)
            .forEach((hoaDonId) => {
              showFileEditor({
                phieu: item,
                nbDotDieuTriId: nbDotDieuTriId,
                id: phieuHoanTraId,
                ma: item.ma,
                maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                  ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                  : "",
                mhParams,
                hoaDonId: hoaDonId,
              });
            });
        } else {
          showFileEditor({
            phieu: item,
            nbDotDieuTriId: nbDotDieuTriId,
            id: phieuHoanTraId,
            ma: item.ma,
            maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
              ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
              : "",
            mhParams,
          });
        }
      }
    } else {
      if (checkIsPhieuKySo(item)) {
        refModalSignPrint.current &&
          refModalSignPrint.current.showToSign({
            phieuKy: item,
            payload: {
              nbDotDieuTriId: nbDotDieuTriId,
              maManHinh: "004",
              maViTri: "00402",
              id: phieuHoanTraId,
            },
          });
      } else {
        try {
          showLoading();
          let params = {
            listPhieus: [item],
            id: phieuHoanTraId,
            showError: true,
            ...((item.ma === "P879" || item.ma === "P1287") && {
              hoaDonId: chiTietPhieuDoiTra?.dsHoaDonGocId,
            }),
            ...(item.ma === "P1253" && {
              nbDotDieuTriId: nbDotDieuTriId,
              chiDinhTuDichVuId: phieuHoanTraId,
            }),
          };
          const { finalFile, dsPhieu } = await getFilePhieuIn(params);
          if ((dsPhieu || []).every((x) => x?.loaiIn == 20)) {
            openInNewTab(finalFile);
          } else {
            printProvider.printPdf(dsPhieu);
          }
        } catch (error) {
          console.log("error", error);
        } finally {
          hideLoading();
        }
      }
    }
  };

  const menu = useMemo(() => {
    return (
      <Menu
        items={(state.listPhieu || [])
          .filter((item) => {
            return !(
              item.ma === "P304" &&
              [10, 20, 30].includes(chiTietPhieuDoiTra?.trangThai)
            );
          })
          .map((item, index) => ({
            key: index,
            label: (
              <a href={() => false} onClick={onPrintPhieu(item)}>
                {item.ten || item.tenBaoCao}
              </a>
            ),
          }))}
      />
    );
  }, [state.listPhieu, chiTietPhieuDoiTra]);

  const onCapNhatHoanNganHang = () => {
    hoanTienNganHang({
      id: phieuHoanTraId,
      maGiaoDich: chiTietPhieuDoiTra?.maGiaoDich,
    });
  };

  const onHoanTraThuoc = () => {
    const data = chiTietPhieuDoiTra?.dsDichVuHoan;
    if (data?.length) {
      refModalHoanTraThuoc.current &&
        refModalHoanTraThuoc.current.show(
          {
            data: data,
            selectedRowKeys: data.map((i) => i.id),
            phieuHoanTraId,
          },
          () => {
            getChiTietPhieuDoiTra({ id: phieuHoanTraId }).then((res) => {
              if (res.dsDichVuHoan) {
                getThongTinPhieuThu(res.dsDichVuHoan[0]?.phieuThuId);
              }
            });
          }
        );
    } else {
      message.error(t("khamBenh.chiDinh.khongCoDichVuThoaManDieuKienDeHoan"));
    }
  };

  const onHuyGuiDuyet = async () => {
    refNhapLyDo.current &&
      refNhapLyDo.current.show(
        {
          title: t("thuNgan.lyDoHuyGuiDuyet"),
          message: t("thuNgan.dienLyDoHuyGuiDuyet"),
        },
        async (lyDo) => {
          try {
            showLoading();
            await huyGuiDuyetTraThuoc({
              id: phieuHoanTraId,
              lyDo,
              khoId: getKho()?.id,
            });
            getChiTietPhieuDoiTra({ id: phieuHoanTraId });
          } catch (error) {
            console.error(error);
          } finally {
            hideLoading();
          }
        }
      );
  };

  const onHuyYeuCau = async () => {
    try {
      showLoading();
      await huyYeuCauTraThuoc({
        id: phieuHoanTraId,
        dsDichVu: (chiTietPhieuDoiTra?.dsDichVuHoan || []).map((x) => ({
          nbDichVuCuId: x.nbDichVuCuId,
        })),
      });
      getChiTietPhieuDoiTra({ id: phieuHoanTraId });
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  return (
    <MainPage
      breadcrumb={[
        { title: t("thuNgan.thuNgan"), link: "/thu-ngan" },
        {
          title: t("thuNgan.danhSachPhieuYeuCauHoan"),
          link:
            "/thu-ngan/ds-phieu-yeu-cau-hoan" +
            transformObjToQueryString(locationState),
        },
        {
          title: t("thuNgan.chiTietYeuCauPhieuHoan"),
          link: `/thu-ngan/chi-tiet-phieu-hoan-tra/${maHoSo}/${phieuHoanTraId}/${nbDotDieuTriId}`,
        },
      ]}
      title={
        <div className="header">
          <p className="title">{t("thuNgan.chiTietYeuCauPhieuHoan")}</p>
          <div className="header-action">
            {!state.showSearch && (
              <Tooltip
                placement="topLeft"
                title={t("thuNgan.timKiemPhieuYeuCauHoan")}
              >
                <SVG.IcSearch
                  className="action-btn"
                  style={{ marginRight: 8 }}
                  onClick={() => {
                    setState({
                      showSearch: true,
                    });
                  }}
                />
              </Tooltip>
            )}
            {state.showSearch && (
              <InputSearch width={"160px"} focus={state.focus}>
                <SVG.IcSearch />
                <Input
                  placeholder={t("thuNgan.nhapSoPhieu")}
                  onBlur={() => {
                    setState({ focus: false });
                  }}
                  onFocus={() => {
                    setState({ focus: true });
                  }}
                  onChange={handleSearch}
                  onPressEnter={handleSearch}
                />
                <SVG.IcQrCode />
              </InputSearch>
            )}
            <InputSearch
              width={state.showSearch ? "220px" : "350px"}
              focus={state.focus2}
            >
              <BarcodeScannerInput
                placeholder={t("thuNgan.timKiemTheoMaNbMaHoSoTenNbQrNbRfIdF6")}
                value={state?.valueSearch}
                onChange={onChangeCode}
                onSubmit={(params) => {
                  onSubmitSearch(params);
                  setState({ valueSearch: "" });
                }}
                onBlur={() => {
                  setState({ focus2: false });
                }}
                onFocus={() => {
                  setState({ focus2: true });
                }}
                showIcon={false}
              />
              <SVG.IcQrCode />
            </InputSearch>

            <Tooltip
              placement="topLeft"
              title={t("thuNgan.danhSachPhieuYeuCauHoan")}
            >
              <SVG.IcList className="icon-list" onClick={handleClick} />
            </Tooltip>
            {checkRole([ROLES["THU_NGAN"].XOA_PHIEU_HOAN_TRA_THUOC]) &&
              chiTietPhieuDoiTra?.trangThai == 10 && (
                <Tooltip
                  placement="topLeft"
                  title={t("thuNgan.xoaPhieuHoanDoiTraDichVu")}
                  className="cursor-pointer"
                >
                  <SVG.IcDelete className="icon-list" onClick={onHuyYeuCau} />
                </Tooltip>
              )}
          </div>
        </div>
      }
      titleRight={
        <>
          {checkRole([ROLES.THU_NGAN.HIEN_THI_HOAN_TRA_THUOC]) && <ChonKho />}
          {checkRole([ROLES.THU_NGAN.CHON_CA_LAM_VIEC]) && <ChonCaLamViec />}
          <ChonQuay
            dsLoaiQuay={LOAI_QUAY.THU_NGAN}
            cacheKey={CACHE_KEY.DATA_NHA_TAM_UNG}
            onChange={(value) => {
              setState({ nhaTamUng: value });
            }}
          />
        </>
      }
      actionRight={
        <>
          {chiTietPhieuDoiTra?.loai === 50 &&
            chiTietPhieuDoiTra?.trangThai != 40 &&
            checkRole([ROLES["THU_NGAN"].HIEN_THI_HOAN_TRA_THUOC]) && (
              <Button type="primary" iconHeight={15} onClick={onHoanTraThuoc}>
                <span>{t("thuNgan.hoanTraThuoc")}</span>
              </Button>
            )}
          {chiTietPhieuDoiTra?.trangThai == 20 &&
            chiTietPhieuDoiTra?.loai === 50 &&
            checkRole([ROLES["THU_NGAN"].HUY_YEU_CAU_GUI_DUYET_TRA_THUOC]) && (
              <Button type="primary" iconHeight={15} onClick={onHuyGuiDuyet}>
                <span>{t("khoMau.huyGuiDuyet")}</span>
              </Button>
            )}
          {thongTinBenhNhanTongHop?.maNganHang == dataMA_DOI_TAC_HOAN_TIEN &&
            !!thongTinBenhNhanTongHop?.soTaiKhoan && (
              <Button
                type="primary"
                iconHeight={15}
                onClick={onCapNhatHoanNganHang}
              >
                <span>{t("thuNgan.capNhatHoanNganHang")}</span>
              </Button>
            )}

          {chiTietPhieuDoiTra?.trangThai == 20 && (
            <AuthWrapper accessRoles={[ROLES["THU_NGAN"].TACH_PHIEU_HOAN]}>
              <Button
                type="primary"
                iconHeight={15}
                onClick={onChuyenPhieuHoan}
              >
                <span>{t("thuNgan.tachPhieuHoan")}</span>
              </Button>
            </AuthWrapper>
          )}

          {chiTietPhieuDoiTra?.trangThai !== 40 &&
            checkRoleOr([
              ROLES["THU_NGAN"].XAC_NHAN_HOAN_DICH_VU,
              ROLES["THU_NGAN"].XAC_NHAN_HOAN_DICH_VU_DA_XUAT_HDDT,
              ROLES["THU_NGAN"].CHI_HOAN_NHA_THUOC,
            ]) && (
              <Button
                loading={state.isLoading}
                type="primary"
                onClick={handleSubmit}
                rightIcon={
                  <SVG.IcSuccess color={"var(--color-blue-primary)"} />
                }
                iconHeight={15}
              >
                <span>{t("thuNgan.xacNhanHoan")}</span>
              </Button>
            )}
          <Dropdown
            overlay={menu}
            trigger="click"
            open={state.openPhieu && !state.loadingPhieu}
            onOpenChange={(open) => {
              setState({ openPhieu: open });
            }}
          >
            <Button
              type="primary"
              rightIcon={<SVG.IcPrint />}
              iconHeight={15}
              onClick={getDanhSachPhieu}
              loading={state.loadingPhieu}
            >
              <span>{t("common.inGiayTo")}</span>
            </Button>
          </Dropdown>
        </>
      }
    >
      <Col span={24} style={{ marginBottom: "1rem" }}>
        <Row
          gutter={[16, 16]}
          style={{ marginTop: "15px", width: "100%", height: "100%" }}
          className="main-content"
        >
          <Col span={17} className="thong-tin-benh-nhan">
            <ThongTinBenhNhan nbDotDieuTriId={nbDotDieuTriId} />
            {/* <ThongTinBenhNhan maHoSo={maHoSo} soPhieu={soPhieu} /> */}
          </Col>
          <Col span={7} className="thong-tin-so-tien">
            <ThongTinSoTien />
          </Col>
          <Col span={17} className="bang-thong-tin">
            <BangThongTin soPhieu={phieuHoanTraId} />
          </Col>
          <Col span={7} className="thong-tin-phieu">
            <ThongTinPhieu
              setStateParent={setState}
              phuongThucTtId={state.phuongThucTtId}
            />
          </Col>
        </Row>
      </Col>
      <ModalChuyenPhieuHoan
        ref={refModalChuyenPhieuHoan}
        nbDotDieuTriId={nbDotDieuTriId}
      />
      <ModalSignPrint ref={refModalSignPrint} />
      <ModalNhapLyDo ref={refNhapLyDo} />
      <ModalChonPTTT ref={refChonPhuongThuc} />
      <ModalInPhieuTomTatBenhAn ref={refModalInPhieuTomTatBA} />
      <ModalHoanTraThuoc ref={refModalHoanTraThuoc} />
    </MainPage>
  );
};

export default ChiTietPhieuYeuCauHoan;
