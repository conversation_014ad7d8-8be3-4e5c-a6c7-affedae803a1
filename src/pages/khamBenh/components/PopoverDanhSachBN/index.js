import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useRef,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { GlobalStyle, Main } from "./styled";
import {
  HeaderSearch,
  InputTimeout,
  TableWrapper,
  Pagination,
  Popover,
  Checkbox,
  Tooltip,
  Select,
} from "components";
import {
  ENUM,
  HOTKEY,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_DICH_VU,
  YES_NO,
} from "constants/index";
import stringUtils from "mainam-react-native-string-utils";
import moment from "moment";
import { createRef } from "react";
import { useTranslation } from "react-i18next";
import {
  useConfirm,
  useGuid,
  useStore,
  useThietLap,
  useWindowSize,
} from "hooks";
import { SVG } from "assets";
import { useEnum } from "hooks";
import { message } from "antd";
import { checkKhamKLKhacBacSi } from "pages/khamBenh/utils";
import { checkRole } from "lib-utils/role-utils";
import { refConfirm } from "app";

export const PopoverDanhSachBN = forwardRef((props, ref) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const layerId = useGuid();
  const refInputNhapSoKham = useRef(null);
  const refMaHoSo = useRef(null);
  const refTenNguoiBenh = useRef(null);
  const windowSize = useWindowSize();
  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const { width } = props;
  const [KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM] = useThietLap(
    THIET_LAP_CHUNG.KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM,
    "FALSE"
  );
  const [dataKHONG_HIEN_THI_NGUOI_BENH_CHUA_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.KHONG_HIEN_THI_NGUOI_BENH_CHUA_THANH_TOAN
  );
  const [dataKHAM_BENH_AN_TINH_NANG_GOI_BO_QUA_TAI_DS_NGUOI_BENH] = useThietLap(
    THIET_LAP_CHUNG.KHAM_BENH_AN_TINH_NANG_GOI_BO_QUA_TAI_DS_NGUOI_BENH,
    "FALSE"
  );
  const [dataTEKMEDI_ON_OFF] = useThietLap(THIET_LAP_CHUNG.TEKMEDI_ON_OFF);
  const [dataCHI_HIEN_THI_STT_KHAM] = useThietLap(
    THIET_LAP_CHUNG.CHI_HIEN_THI_STT_KHAM
  );
  const [dataGOI_STT_PK_MH_SINH_HIEU] = useThietLap(
    THIET_LAP_CHUNG.GOI_STT_PK_MH_SINH_HIEU
  );
  const isHienThiSttKham = dataCHI_HIEN_THI_STT_KHAM?.eval();

  const { nhanVienId, full_name } = useStore(
    "auth.auth",
    {},
    { fields: "full_name, nhanVienId" }
  );
  const [state, _setState] = useState({
    show: false,
    data: {},
    position: 0,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const { listData, totalElements, page, size, dataSearch, dataSortColumn } =
    useSelector((state) => state.nbKhamBenh);

  const { infoNb, dangKhamError, listPhongKham } = useSelector(
    (state) => state.khamBenh
  );
  const {
    nbKhamBenh: { onSearch, onSizeChange, onSortChange, onChangeInputSearch },
    khamBenh: { kiemTraTrangThaiLoadNguoiBenh, boQuaKham, updateData },
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
    goiSo: { kiemTraNbDaDenLuot },
  } = useDispatch();

  // useEffect(() => {
  //   if (dangKhamError && refDangKhamError.current != dangKhamError?.message) {
  //     debugger
  //     refDangKhamError.current = dangKhamError?.message;
  //     showConfirm(
  //         {
  //           showBtnOk: false,
  //           title: t("common.thongBao"),
  //           content: dangKhamError?.message,
  //           rightCancelButton: true,
  //           cancelText: t("common.dong"),
  //         },
  //         () => {},
  //         () => {
  //           updateData({
  //             dangKhamError: "",
  //           });
  //         }
  //       );
  //   }
  // }, [dangKhamError]);

  useEffect(() => {
    renderData();
  }, [listData]);

  const renderData = () => {
    let data = listData.map((item) => {
      let age =
        item.thangTuoi <= 36
          ? ` - ${item.tuoi2}`
          : ` - ${item.tuoi} ${t("common.tuoi")}`;
      return {
        ...item,
        mucHuongTheBhyt: item.mucHuongBhyt,
        thongTin: `${item.tenNb}${age}${
          item.tenTinhThanhPho ? " - " + item.tenTinhThanhPho : ""
        }`,
      };
    });
    setState({ data });
  };

  useImperativeHandle(ref, () => ({
    show: (option = {}) => {
      const { params, title, right, popoverKey } = option;
      setState({
        show: true,
        title,
        right,
        popoverKey,
      });
      const now = moment();
      onChangeInputSearch({
        ignoreDataSearch: true,
        tuThoiGianThucHien: now.startOf("day").format("YYYY-MM-DD HH:mm:ss"),
        denThoiGianThucHien: now.endOf("day").format("YYYY-MM-DD HH:mm:ss"),
        dsTrangThaiHoan: [0, 10],
        khongThucHien: false,
        ...(dataKHONG_HIEN_THI_NGUOI_BENH_CHUA_THANH_TOAN?.eval()
          ? { dieuKienThanhToan: true }
          : {}),
        ...params,
      });

      onAddLayer({ layerId: layerId });
      onRegisterHotkey({
        layerId: layerId,
        hotKeys: [
          {
            keyCode: HOTKEY.F6, //F6
            onEvent: () => {
              refInputNhapSoKham.current && refInputNhapSoKham.current.focus();
            },
          },
        ],
      });
    },
  }));
  useEffect(() => {
    if (!state.show) {
      onRemoveLayer({ layerId: layerId });
    }
  }, [state.show]);

  useEffect(() => {
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, []);

  useEffect(() => {
    if (state.show) {
      setTimeout(() => {
        refTenNguoiBenh.current &&
          refTenNguoiBenh.current.setValue(state.tenNb);
        refMaHoSo.current && refMaHoSo.current.setValue(state.maHoSo);
      }, [1000]);
    }
  }, [state.show, state.tenNb, state.maHoSo]);

  const onSetNbTiepTheo = (record, chuyenTrangThai) => {
    return new Promise((resolve, reject) => {
      kiemTraTrangThaiLoadNguoiBenh({
        data: record,
        dichVuId: record.id,
        nbDotDieuTriId: record.nbDotDieuTriId,
        chuyenTrangThai,
        nbId: record.id,
      })
        .then((s) => {
          resolve(true);
        })
        .catch((e) => {
          showConfirm(
            {
              showBtnOk: true,
              title: t("common.thongBao"),
              content: e,
              cancelText: t("common.dong"),
              okText: t("common.xacNhan"),
              typeModal: "warning",
            },
            () => {
              setState({ show: false });
              kiemTraTrangThaiLoadNguoiBenh({
                data: record,
                dichVuId: record.id,
                nbDotDieuTriId: record.nbDotDieuTriId,
                chuyenTrangThai,
                forceUpdate: true,
                nbId: record.id,
              })
                .then((s) => {
                  resolve(true);
                })
                .catch((e) => {
                  reject(e);
                });
            },
            () => {
              reject();
            }
          );
        });
    });
  };

  const onKiemTraNbDaDenSTT = async (record) => {
    if (
      dataTEKMEDI_ON_OFF?.eval() &&
      !checkRole([
        ROLES["KHAM_BENH"].BO_QUA_KHONG_BAT_BUOC_CHECK_NB_CHUA_DEN_STT,
      ]) &&
      (record.thoiGianVaoVien
        ? moment(record.thoiGianVaoVien).isSame(moment(), "day")
        : true)
    ) {
      const isDenLuot = await kiemTraNbDaDenLuot({
        maPhong: record?.maPhongThucHien,
        maHoSo: record?.maHoSo,
      });

      if (!isDenLuot) {
        refConfirm.current &&
          refConfirm.current.show({
            title: t("common.canhBao"),
            content: t("khamBenh.nbTenNbChuaDenLuotVaoPhong", {
              tenNb: record.tenNb,
            }),
            cancelText: t("common.quayLai"),
            classNameOkText: "button-warning",
            showBtnOk: false,
            typeModal: "warning",
          });
      }

      return isDenLuot;
    }

    return true;
  };

  const onRow = (record) => {
    return {
      onClick: async () => {
        if (
          !(await checkKhamKLKhacBacSi(
            record,
            nhanVienId,
            KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM
          ))
        ) {
          message.error(
            t("khamBenh.khongChoPhepThucHienKhamKetLuan", {
              bacSiKham: record.tenBacSiKham,
              bacSiHienTai: full_name,
            })
          );
          return;
        } else {
          if (
            state.popoverKey === "doiKham" &&
            [
              TRANG_THAI_DICH_VU.CHO_KHAM,
              TRANG_THAI_DICH_VU.CHUAN_BI_KHAM,
              TRANG_THAI_DICH_VU.DA_CHECKIN_KHAM,
              TRANG_THAI_DICH_VU.BO_QUA,
            ].includes(record.trangThai)
          ) {
            if (!(await onKiemTraNbDaDenSTT(record))) {
              return;
            }
          }

          setState({ show: false });

          onSetNbTiepTheo(record, false);
        }
      },
    };
  };

  const onClickSort = (key, value) => {
    onSortChange({
      [key]: value,
    });
  };

  const onSearchInput = (key) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else value = e;

    onChangeInputSearch({
      [key]: value,
      dsTrangThaiHoan: [0, 10],
    });
  };

  const onClickBoQua = (record) => () => {
    boQuaKham({
      loadNbTiepTheo: false,
      id: record.id,
      trangThai: record.trangThai,
      isShowMessage: true,
    }).then((s) => {
      record.trangThai = s?.data?.nbDvKyThuat?.trangThai;
      setState({ data: [...state.data] });
    });
  };

  const onClickGoi = (record) => async (e) => {
    e.stopPropagation();

    if (
      !(await checkKhamKLKhacBacSi(
        record,
        nhanVienId,
        KHONG_DUOC_KHAM_KL_NB_KHAC_BSKHAM
      ))
    ) {
      message.error(
        t("khamBenh.khongChoPhepThucHienKhamKetLuan", {
          bacSiKham: record.tenBacSiKham,
          bacSiHienTai: full_name,
        })
      );
      return;
    }

    if (
      [
        TRANG_THAI_DICH_VU.CHO_KHAM,
        TRANG_THAI_DICH_VU.CHUAN_BI_KHAM,
        TRANG_THAI_DICH_VU.DA_CHECKIN_KHAM,
        TRANG_THAI_DICH_VU.BO_QUA,
      ].includes(record.trangThai)
    ) {
      if (!(await onKiemTraNbDaDenSTT(record))) {
        return;
      }
    }

    onSetNbTiepTheo(record, true).then((s) => {
      setState({ show: false });
    });
  };

  const renderGoiButton = (record) => {
    if (
      dataKHAM_BENH_AN_TINH_NANG_GOI_BO_QUA_TAI_DS_NGUOI_BENH?.eval() ||
      dataGOI_STT_PK_MH_SINH_HIEU?.eval()
    )
      return null;
    const trangThai = record?.trangThai;
    switch (trangThai) {
      case TRANG_THAI_DICH_VU.CHO_KHAM:
      case TRANG_THAI_DICH_VU.CHUAN_BI_KHAM:
      case TRANG_THAI_DICH_VU.DA_CHECKIN_KHAM:
      case TRANG_THAI_DICH_VU.DA_CHECKIN_KET_LUAN:
      case TRANG_THAI_DICH_VU.CHUAN_BI_KET_LUAN:
      case TRANG_THAI_DICH_VU.CHO_KET_LUAN:
      case TRANG_THAI_DICH_VU.BO_QUA:
      case TRANG_THAI_DICH_VU.BO_QUA_KET_LUAN:
        //cho phép button Gọi nhấn
        return (
          <div className="btn-action" onClick={onClickGoi(record)}>
            {t("khamBenh.goi")}
          </div>
        );

      case TRANG_THAI_DICH_VU.DA_DUYET:
        //button gọi không cho phép
        return (
          <div disabled className="btn-action">
            {t("khamBenh.goi")}
          </div>
        );

      case TRANG_THAI_DICH_VU.DANG_THUC_HIEN_DICH_VU:
      case TRANG_THAI_DICH_VU.DA_KET_LUAN:
      default:
        break;
    }
  };

  const renderBoQuaButton = (record) => {
    if (dataKHAM_BENH_AN_TINH_NANG_GOI_BO_QUA_TAI_DS_NGUOI_BENH?.eval())
      return null;
    const trangThai = record?.trangThai;
    switch (trangThai) {
      case TRANG_THAI_DICH_VU.DANG_KHAM:
      case TRANG_THAI_DICH_VU.CHO_KHAM:
      case TRANG_THAI_DICH_VU.CHUAN_BI_KHAM:
      case TRANG_THAI_DICH_VU.CHO_KET_LUAN:
      case TRANG_THAI_DICH_VU.CHUAN_BI_KET_LUAN:
      case TRANG_THAI_DICH_VU.DANG_KET_LUAN:
        //cho phép button Gọi nhấn
        return (
          <div className="btn-action" onClick={onClickBoQua(record)}>
            {t("common.boQua")}
          </div>
        );
      default:
        break;
    }
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} isTitleCenter={true} />,
      width: "30px",
      dataIndex: "index",
      key: "index",
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.dsBenhNhan.soKham")}
          sort_key={isHienThiSttKham ? "stt" : "stt2"}
          dataSort={dataSortColumn[isHienThiSttKham ? "stt" : "stt2"] || 0}
          onClickSort={onClickSort}
          search={
            <InputTimeout
              ref={refInputNhapSoKham}
              placeholder={t("khamBenh.dsBenhNhan.nhapSoKham")}
              onChange={onSearchInput(isHienThiSttKham ? "stt" : "stt2")}
            />
          }
        />
      ),
      width: "80px",
      dataIndex: isHienThiSttKham ? "stt" : "stt2",
      key: isHienThiSttKham ? "stt" : "stt2",
      render: (field, item, list) => {
        const { trangThai, khamSucKhoe } = item;
        let prefix = null;
        [
          {
            waiting: [20, 30, 40, 100, 110, 120],
            icon: <SVG.IcWaiting style={{ marginRight: 2 }} />,
          },
          {
            pending: [60, 70, 140],
            icon: <SVG.IcPending style={{ marginRight: 2 }} />,
          },
          {
            finish: [150, 160],
            icon: (
              <SVG.IcSuccess
                color={"var(--color-green-primary)"}
                style={{ marginRight: 2 }}
              />
            ),
          },
        ].some((item) => {
          const [[, values], [_, icon]] = Object.entries(item);
          if (values.includes(trangThai)) {
            prefix = icon;
            return true;
          }
          return false;
        });
        return (
          <div className="flex flex-center">
            <Tooltip
              title={
                listTrangThaiDichVu.find((i) => i.id === trangThai)?.ten || ""
              }
              placement="left"
            >
              {prefix ? prefix : null}
            </Tooltip>{" "}
            {field} {khamSucKhoe && "- SKTQ"}
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.dsBenhNhan.ngayDangKy")}
          sort_key="thoiGianVaoVien"
          dataSort={dataSortColumn["thoiGianVaoVien"] || 0}
          onClickSort={onClickSort}
          search={
            <InputTimeout
              placeholder={t("khamBenh.dsBenhNhan.nhapNgayDangKy")}
              onChange={onSearchInput("thoiGianVaoVien")}
            />
          }
        />
      ),
      render: (item) => {
        return moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
      width: "100px",
      dataIndex: "thoiGianVaoVien",
      key: "thoiGianVaoVien",
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.dsBenhNhan.tenTuoiDiaChi")}
          sort_key="tenNb"
          dataSort={dataSortColumn["tenNb"] || 0}
          onClickSort={onClickSort}
          search={
            <InputTimeout
              refWrap={refTenNguoiBenh}
              placeholder={t("khamBenh.dsBenhNhan.nhapTenTuoiDiaChi")}
              onChange={onSearchInput("tenNb")}
            />
          }
        />
      ),
      width: "120px",
      dataIndex: "thongTin",
      key: "thongTin",
      render: (item) => <div style={{ fontWeight: "bold" }}>{item}</div>,
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("common.uuTien")}
          searchSelect={
            <Select
              data={YES_NO}
              defaultValue=""
              onChange={onSearchInput("uuTien")}
              placeholder={t("khamBenh.dsBenhNhan.uuTien")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "uuTien",
      key: "uuTien",
      align: "center",
      show: true,
      i18Name: "tiepDon.uuTien",

      render: (item, data) => {
        return <Checkbox checked={item} disabled></Checkbox>;
      },
    },
    {
      title: (
        <HeaderSearch isTitleCenter={true} title={t("tiepDon.daCheckin")} />
      ),
      width: 40,
      dataIndex: "daCheckin",
      key: "daCheckin",
      align: "center",
      show: true,
      i18Name: "tiepDon.daCheckin",
      render: (item, data) => {
        const checked = TRANG_THAI_DICH_VU.DA_CHECKIN_DICH_VU.includes(
          data.trangThai
        );
        return <Checkbox checked={checked} disabled></Checkbox>;
      },
    },
    {
      title: <HeaderSearch title={t("common.thaoTac")} isTitleCenter={true} />,
      width: "60px",
      dataIndex: "action",
      key: "action",
      hidden: state.popoverKey === "daKetLuanChuaLinhThuoc",
      render: (item, record) => {
        return (
          <div className="action-group">
            {renderGoiButton(record)}
            {renderBoQuaButton(record)}
          </div>
        );
      },
    },
  ];

  const handleChangePage = (page) => {
    onSearch({ page: page - 1 }, true);
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size: size, dataSortColumn, dataSearch });
  };

  const rowClassName = (record) => {
    return record.id === infoNb?.id ? "active" : "";
  };

  const onVisibleChange = (e) => {
    setState({
      show: e,
      data: [],
    });
  };

  const renderContent = () => (
    <Main width={width}>
      <TableWrapper
        rowClassName={rowClassName}
        columns={columns}
        dataSource={state.data}
        onRow={onRow}
        scroll={{ y: 450 }}
        rowKey={(record) => `${record.id}-${record.tenNb}`}
        styleWrap={{
          height: Math.min(
            (state.data || []).length * 60 + 120,
            windowSize.height * 0.5
          ),
          minHeight: 260,
        }}
      />
      {totalElements ? (
        <Pagination
          listData={state.data}
          onChange={handleChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
        />
      ) : null}
    </Main>
  );

  return (
    <>
      <GlobalStyle $right={state.right} />
      <Popover
        title={`${t("khamBenh.titleDanhSachNguoiBenh")} ${state.title || ""}`}
        trigger="click"
        placement="bottomRight"
        overlayClassName="dsBenhNhan-popover"
        visible={state.show}
        width={680}
        zIndex={999}
        content={renderContent()}
        onVisibleChange={onVisibleChange}
      />
    </>
  );
});

export default PopoverDanhSachBN;
