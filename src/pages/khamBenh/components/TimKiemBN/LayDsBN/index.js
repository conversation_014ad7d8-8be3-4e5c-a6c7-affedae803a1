import { Row, Col } from "antd";
import React, { useRef, useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import { Main } from "./styled";
import { checkRole } from "lib-utils/role-utils";
import { HOTKEY, ROLES, THIET_LAP_CHUNG } from "constants/index";
import { useTranslation } from "react-i18next";
import { Button } from "components";
import { useConfirm, useStore, useThietLap } from "hooks";
import { SVG } from "assets";
import { useParams } from "react-router-dom";

export const LayDSBN = ({
  layerId,
  onShowDanhSachBenhNhan,
  phongThucHienId,
}) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const refFuncGetNbTiepTheo = useRef(null);
  const refDsNb = useRef(null);
  const [dataHIEN_THI_KET_NOI_MEMO] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_KET_NOI_MEMO
  );
  const [dataGOI_STT_PK_MH_SINH_HIEU] = useThietLap(
    THIET_LAP_CHUNG.GOI_STT_PK_MH_SINH_HIEU
  );
  const {
    khamBenh: { boQuaKham, nguoiBenhTiepTheo, kiemTraTrangThaiLoadNguoiBenh },
    phimTat: { onRegisterHotkey },
    khamBenh: { getStatisticsRoom },
  } = useDispatch();
  const { phongThucHienId: _phongThucHienId } = useParams();

  const infoNb = useStore("khamBenh.infoNb", {});
  const userName = useStore("auth.auth.username", "");
  const onShowDsNb = () => {
    onShowDanhSachBenhNhan && onShowDanhSachBenhNhan({ search: false });
  };
  const onBoQuaNb = () => {
    boQuaKham({ loadNbTiepTheo: true });
  };
  const openMemoPopup = (pid) => (e) => {
    const url = `https://note.hpimc.info/Memo?pid=${pid}&username=${userName}`;
    window.open(
      url,
      "_blank",
      "width=800,height=600,resizable=yes,scrollbars=yes"
    );
  };
  const listPhongKham = useStore("khamBenh.listPhongKham");

  const isPhongHopLe = useMemo(() => {
    if (_phongThucHienId && listPhongKham?.length) {
      let dsPhongThucHienId = listPhongKham.map((x) => x.id);
      return dsPhongThucHienId.includes(+_phongThucHienId);
    }
    if (!_phongThucHienId)
      //không không có phòng thực hiện thì true
      return true;
    return false; // ngược lại thì chờ trả về listPhongKham > 0
  }, [_phongThucHienId, listPhongKham]);

  const onGetNbTiepTheo = () => {
    if (!isPhongHopLe) return;
    kiemTraTrangThaiLoadNguoiBenh()
      .then((s) => {
        nguoiBenhTiepTheo({});
        getStatisticsRoom({ phongThucHienId });
      })
      .catch((e) => {
        showConfirm(
          {
            content: e,
            cancelText: t("common.huy"),
            okText: t("common.xacNhan"),
            showBtnOk: true,
            typeModal: "warning",
          },
          () => {
            nguoiBenhTiepTheo({});
            getStatisticsRoom({ phongThucHienId });
          },
          () => {}
        );
      });
  };
  refFuncGetNbTiepTheo.current = onGetNbTiepTheo;
  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1, //F1
          onEvent: () => {
            refFuncGetNbTiepTheo.current && refFuncGetNbTiepTheo.current();
          },
        },
        {
          keyCode: HOTKEY.F7, //F1
          onEvent: (e) => {
            refDsNb.current && refDsNb.current.click();
          },
        },
      ],
    });
  }, []);
  return (
    <Main>
      {checkRole([ROLES["KHAM_BENH"].GOI_NB_TIEP_THEO]) &&
        isPhongHopLe &&
        !dataGOI_STT_PK_MH_SINH_HIEU.eval() && (
          <Button
            fit={true}
            type="primary"
            onClick={onGetNbTiepTheo}
            rightIcon={<SVG.IcExtend />}
          >
            {t("khamBenh.nbTiepTheo")}
          </Button>
        )}
      <Button
        fit={true}
        type="default"
        content=""
        onClick={onShowDsNb}
        rightIcon={<SVG.IcList />}
        ref={refDsNb}
      >
        {t("khamBenh.danhSachNb")}
      </Button>
      {!(
        (infoNb?.khamSucKhoe || infoNb?.loaiDoiTuongKsk) &&
        infoNb?.trangThaiKsk != 20
      ) && (
        <Button
          onClick={onBoQuaNb}
          rightIcon={<SVG.IcCancel />}
          minWidth={100}
          type="default"
        >
          {t("common.boQua")}
        </Button>
      )}
      {dataHIEN_THI_KET_NOI_MEMO?.eval() && (
        <Button
          onClick={openMemoPopup(infoNb?.maNb)}
          rightIcon={<SVG.IcMemo />}
          minWidth={100}
          borderColor="#D56D05"
          color="#D56D05"
          type="default"
        >
          Memo
        </Button>
      )}
    </Main>
  );
};

export default LayDSBN;
