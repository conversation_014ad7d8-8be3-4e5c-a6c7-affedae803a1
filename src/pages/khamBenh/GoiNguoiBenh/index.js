import { useMutation, useQuery } from '@tanstack/react-query';
import { BounceInLeft, FadeIn } from 'animations';
import { message } from 'antd';
import { Page, Select } from 'components';
import { LOAI_DICH_VU } from 'constants/index';
import nbDvCdhaTdcnPtTtProvider from 'data-access/nb-dv-cdha-tdcn-pt-tt-provider';
import nbDvKhamProvider from 'data-access/nb-dv-kham-provider';
import nbDvXetNghiemProvider from 'data-access/nb-dv-xet-nghiem-provider';
import { useConfirm, useListAll, useLoading } from 'hooks';
import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import styled from 'styled-components';
import { showError } from 'utils/message-utils';

const DS_SO_LUONG_GOI = [
  { id: 1, ten: "1" },
  { id: 2, ten: "2" },
  { id: 3, ten: "3" },
  { id: 4, ten: "4" },
  { id: 5, ten: "5" }
]
const DS_NB_TIEP_THEO = [1, 2, 3, 4, 5];
const GoiNguoiBenhKhamBenh = (props) => {
  const { t } = useTranslation();
  const { phongThucHienId, loaiQMS } = useParams();
  const [listAllPhong] = useListAll("phong", { page: "", size: "", active: true });
  const { showLoading, hideLoading } = useLoading();
  const { showConfirm } = useConfirm();
  const [state, _setState] = useState({
    soLuongUuTien: 5,
    soLuongThuong: 5,
  })

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const { title, loaiPhong } = useMemo(() => {
    if (loaiQMS == "qms-kham-benh")
      return { loaiPhong: LOAI_DICH_VU.KHAM, title: "phòng khám" };
    if (loaiQMS == "qms-chan-doan-hinh-anh")
      return { loaiPhong: LOAI_DICH_VU.CDHA, title: "CDHA" };
    if (loaiQMS == "qms-xet-nghiem")
      return { loaiPhong: LOAI_DICH_VU.XET_NGHIEM, title: "Xét nghiệm" };
  }, [loaiQMS])

  const { data: dataGoiSo, refetch } = useQuery({
    queryKey: ["listGoiSoPhongKham", phongThucHienId],
    queryFn: () =>
      (loaiPhong == LOAI_DICH_VU.KHAM ? nbDvKhamProvider : loaiPhong == LOAI_DICH_VU.CDHA ? nbDvCdhaTdcnPtTtProvider : nbDvXetNghiemProvider).getDsNguoiBenhQms({
        dsPhongThucHienId: phongThucHienId,
      }),
    refetchInterval: phongThucHienId ? 5000 : false,
    enabled: !!phongThucHienId,
    retry: 2,
    staleTime: 2000,
    structuralSharing: true,
    refetchOnWindowFocus: false,
  });

  const { mutate: mutateNbTiepTheo } = useMutation({
    mutationFn: async (payload) => {
      return (loaiPhong == LOAI_DICH_VU.KHAM ? nbDvKhamProvider : loaiPhong == LOAI_DICH_VU.CDHA ? nbDvCdhaTdcnPtTtProvider : nbDvXetNghiemProvider).postNbTiepTheo(payload);
    },
  });

  const { listUuTien, listThuong, sttUuTien, sttThuong } = useMemo(() => {
    if (dataGoiSo?.data?.length) {
      const data = dataGoiSo?.data?.find(item => item.phongThucHienId == phongThucHienId);
      const dsTheoTheo = data?.dsTiepTheo?.slice(0, 5);
      const dsDangThucHien = data?.dsDangThucHien;
      const dsDangThucHienUuTien = dsDangThucHien?.filter(item => item.uuTien) || [];
      const dsDangThucHienThuong = dsDangThucHien?.filter(item => !item.uuTien) || [];
      const sttUuTien = dsDangThucHienUuTien?.length ? Math.max(...dsDangThucHienUuTien.map(item => item.stt)) : 0
      const sttThuong = dsDangThucHienThuong?.length ? Math.max(...dsDangThucHienThuong.map(item => item.stt)) : 0
      return {
        listUuTien: dsTheoTheo?.filter(item => item.uuTien) || [],
        listThuong: dsTheoTheo?.filter(item => !item.uuTien) || [],
        sttUuTien,
        sttThuong
      }
    }
    else {
      return {
        listUuTien: [],
        listThuong: [],
        sttUuTien: 0,
        sttThuong: 0
      }
    }
  }, [dataGoiSo, phongThucHienId]);
  //   getData(phongThucHienId);
  // }), 10000);


  const handleGoiNbTiepTheo = useCallback(
    (isUuTien) => () => {
      // showConfirm(
      //   {
      //     title: t("common.thongBao"),
      //     content: t("qms.banCoChacChanMuonGoiSTTTiepTheo"),
      //     cancelText: t("common.huy"),
      //     okText: t("common.dongY"),
      //     showImg: true,
      //     showBtnOk: true,
      //     typeModal: "warning",
      //   },
      //   () => {
      showLoading();
      mutateNbTiepTheo(
        {
          phongThucHienId: phongThucHienId,
          uuTien: isUuTien,
          soLuong: isUuTien ? state.soLuongUuTien : state.soLuongThuong,
        },
        {
          onSuccess: () => {
            message.success(t("qms.goiSTTTiepTheoThanhCong"));
            hideLoading();
            refetch();
          },
          onError: (error) => {
            hideLoading();
            showError(error?.message);
          },
        }
      );
      //   }
      // );
    },
    [
      t,
      showConfirm,
      showLoading,
      hideLoading,
      mutateNbTiepTheo,
      phongThucHienId,
      state.soLuongThuong,
      state.soLuongUuTien,
      refetch,
    ]
  );

  const phong = useMemo(() => {
    return listAllPhong?.find(item => item.id == phongThucHienId);
  }, [phongThucHienId, listAllPhong])

  const titlePage = useMemo(() => {
    if (phong)
      return `Gọi số ${title}: ${phong.ten}`;
  }, [phong, title])
  return (
    <Page title={titlePage} breadcrumb={[
      {
        title: "Gọi số " + title,
      },
    ]}>
      <Container delay={0.5}>
        <Cards>
          <Card variant="priority">
            <h4>STT đang tiếp nhận</h4>
            <h1>{sttUuTien}</h1>
            <h3>Người bệnh ưu tiên</h3>
            <CardRow>
              <span>Số NB 1 lần gọi</span>
              <Select allowClear={true} data={DS_SO_LUONG_GOI} value={state.soLuongUuTien} onChange={e => setState({ soLuongUuTien: e || 5 })}></Select>
            </CardRow>
            <Button variant="priority" onClick={handleGoiNbTiepTheo(true)}>Số thứ tự tiếp theo</Button>
          </Card>

          <Card variant="normal">
            <h4>STT đang tiếp nhận</h4>
            <h1>{sttThuong}</h1>
            <h3>Người bệnh thường</h3>
            <CardRow>
              <span>Số NB 1 lần gọi</span>
              <Select allowClear={true} data={DS_SO_LUONG_GOI} value={state.soLuongThuong} onChange={e => setState({ soLuongThuong: e || 5 })}></Select>
            </CardRow>
            <Button variant="normal" onClick={handleGoiNbTiepTheo(false)}>Số thứ tự tiếp theo</Button>
          </Card>
        </Cards>

        <Lists>
          <List variant="priority">
            <ListHeader variant="priority">
              NGƯỜI BỆNH CHỜ KHÁM ƯU TIÊN <span>{listUuTien?.length || 0} NB</span>
            </ListHeader>
            <ListBody>
              {DS_NB_TIEP_THEO.map((p, i) => {
                const item = listUuTien[i];
                return <ListItem key={i} delay={i * 0.15}>
                  {
                    item && <>
                      <div className="number">{item.stt}</div>
                      <div className="name">{item.tenNb}</div>
                      <div className="age">{item.tuoi2}</div>
                    </>
                  }
                </ListItem>
              })}
            </ListBody>
          </List>

          <List variant="normal">
            <ListHeader variant="normal">
              NGƯỜI BỆNH CHỜ KHÁM THƯỜNG <span>{listThuong?.length || 0} NB</span>
            </ListHeader>
            <ListBody>
              {DS_NB_TIEP_THEO.map((p, i) => {
                const item = listThuong[i];
                return <ListItem key={i} delay={i * 0.1}>
                  {
                    item && <>
                      <div className="number">{item.stt}</div>
                      <div className="name">{item.tenNb}</div>
                      <div className="age">{item.tuoi2}</div>
                    </>
                  }
                </ListItem>
              })}
            </ListBody>
          </List>
        </Lists>
      </Container>
    </Page>
  );
}

export default GoiNguoiBenhKhamBenh;


const Container = FadeIn(styled.div`
  display: flex;
  flex-direction: column;
  font-family: 'Inter', sans-serif;
  background: #eef5fa;
  height: 100%;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  color: #123;
`);

const Cards = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 15px;

  @media (max-width: 1024px) {
    flex-direction: column;
  }
`;

const Card = styled.div`
  flex: 1;
  background: white;
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  color: ${props => (props.variant === 'priority' ? '#E53935' : '#2b87d1')};;

  h4 {
    font-weight: 600;
    margin-bottom: 10px;
    color: ${props => (props.variant === 'priority' ? '#E53935' : '#2b87d1')};;
  }

  h1 {
    line-height: normal;
    font-size: clamp(40px,8vh,80px);
    font-weight: 800;
    margin: 10px 0;
    color: ${props => (props.variant === 'priority' ? '#E53935' : '#2b87d1')};

    @media (max-width: 768px) {
      font-size: 48px;
    }
  }

  h3 {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 15px;
    color: ${props => (props.variant === 'priority' ? '#E53935' : '#2b87d1')};
  }

  & .ant-select 
  {
    width: 80px;
    & .ant-select-selection-item{
      color: ${props => (props.variant === 'priority' ? '#E53935' : '#2b87d1')};
    }
    & .ant-select-selector{
      border: 2px solid ${props => (props.variant === 'priority' ? '#E53935' : '#2b87d1')} !important;
    }
  }
`;

const CardRow = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;

  select {
    padding: 6px 10px;
    border-radius: 6px;
    border: 1px solid #ccc;
  }
`;

const Button = styled.button`
  border: none;
  border-radius: 6px;
  padding: 10px 22px;
  font-weight: 600;
  color: white;
  cursor: pointer;
  background: ${props => (props.variant === 'priority' ? 'linear-gradient(135deg, #E53935 0%, #c62828 100%);' : '#2b87d1')};
  transition: all 0.2s ease;

  &:hover {
    opacity: 0.85;
  }
`;

const Lists = styled.div`
  flex: 1;
  display: flex;
  gap: 20px;
  overflow: hidden;
  @media (max-width: 1024px) {
    flex-direction: column;
  }
`;

const List = styled.div`
  flex: 1;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const ListHeader = styled.div`
  background: ${props => (props.variant === 'priority' ? 'linear-gradient(135deg,#E53935 0%,#c62828 100%)' : '#2b87d1')};
  color: white;
  font-weight: 700;
  font-size: 18px;
  padding: 14px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    background: white;
    color: #0a2233;
    border-radius: 20px;
    padding: 3px 10px;
    font-size: 14px;
    font-weight: 600;
  }
`;

const ListBody = styled.div`
  padding: 16px;
  flex: 1;
  overflow: auto;
`;

const ListItem = BounceInLeft(styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f5f8fc;
  border-radius: 12px;
  padding: 12px 16px;
  margin-bottom: 10px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
  min-height: 57px;

  .number {
    background: #24b4d8;
    color: white;
    font-weight: 700;
    font-size: 18px;
    padding: 4px 12px;
    border-radius: 8px;
    min-height: 34px;
    min-width: 36px;
  }

  .name {
    flex: 1;
    text-align: left;
    font-weight: 700;
    margin-left: 10px;
    color: #0c1f34;
    word-break: break-word;
  }

  .age {
    font-weight: 600;
    color: #7a8b9a;
  }
`);