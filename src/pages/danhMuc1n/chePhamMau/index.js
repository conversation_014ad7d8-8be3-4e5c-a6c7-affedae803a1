import React, { useEffect, useState, useRef } from "react";
import { connect, useDispatch } from "react-redux";
import MultiLevelTab from "components/MultiLevelTab";
import DichVuKyThuat from "components/DanhMuc/DichVuKyThuat";
import DichVuKemTheo from "components/DanhMuc/DichVuKemTheo";
import KhoaChiDinh from "components/DanhMuc/KhoaChiDinh";
import <PERSON>hom<PERSON>hiPhi from "components/DanhMuc/NhomChiPhi";
import ThongTinDichVu from "components/DanhMuc/ThongTinDichVu";
import { Button, HomeWrapper } from "components";
import { Main } from "./styled";
import {
  ADD_LAYOUT_COLLAPSE,
  TABLE_LAYOUT_COLLAPSE,
  ADD_LAYOUT,
  TABLE_LAYOUT,
  HOTKEY,
  LOAI_DICH_VU,
} from "constants/index";
import { Col } from "antd";
import { checkRole } from "lib-utils/role-utils";
import { ROLES } from "constants/index";
import Tuy<PERSON>honGia from "components/DanhMuc/TuyChonGia";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { useGuid } from "hooks";

const ID_LOAI_DICH_VU = LOAI_DICH_VU.CHE_PHAM_MAU;

const ServicesPack = (props) => {
  const { t } = useTranslation();
  const refTab = useRef();
  const layerId = useGuid();
  const refClickBtnAdd = useRef();
  const [editStatus, setEditStatus] = useState(false);
  const { currentItem, getListAllDuongDung } = props;
  const [collapseStatus, setCollapseStatus] = useState(false);
  const [state, _setState] = useState({
    showFullTable: false,
  });
  const {
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
    dichVuKho: { onImportDMChePhamMau, onExport },
    dichVuKemTheo: { onImportDichVuKemTheo, onExportDichVuKemTheo },
    tuyChonGia: { onImportTuyChonGia, onExportTuyChonGia },
  } = useDispatch();

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  useEffect(() => {
    getListAllDuongDung({
      page: "",
      size: "",
      active: true,
      sort: "ten,asc",
    });
  }, []);

  // register layerId
  useEffect(() => {
    onAddLayer({ layerId: layerId });
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1, //F1
          onEvent: () => {
            refClickBtnAdd.current && refClickBtnAdd.current();
          },
        },
      ],
    });
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, []);

  const listPanel = [
    {
      title: t("common.thongTinDichVu"),
      key: 1,
      render: () => {
        return (
          <ThongTinDichVu
            loaiDichVu={ID_LOAI_DICH_VU}
            currentItem={props.currentItem}
            roleSave={[ROLES["DANH_MUC"].CHE_PHAM_MAU_THEM]}
            roleEdit={[ROLES["DANH_MUC"].CHE_PHAM_MAU_SUA]}
            editStatus={
              editStatus
                ? !checkRole([ROLES["DANH_MUC"].CHE_PHAM_MAU_SUA])
                : !checkRole([ROLES["DANH_MUC"].CHE_PHAM_MAU_THEM])
            }
            layerId={layerId}
            ignoreFetch={true}
            refTab={refTab}
          />
        );
      },
    },
    {
      key: 2,
      title: t("danhMuc.dichVuKemTheo"),
      render: () => {
        return (
          <DichVuKemTheo
            loaiDichVu={ID_LOAI_DICH_VU}
            isChePhamMau={true}
            dichVuId={currentItem?.id}
            dsLoaiDichVu={[10, 20, 30, 60, 90, 100, 120]}
            roleSave={[ROLES["DANH_MUC"].CHE_PHAM_MAU_THEM]}
            roleEdit={[ROLES["DANH_MUC"].CHE_PHAM_MAU_SUA]}
            editStatus={
              editStatus
                ? !checkRole([ROLES["DANH_MUC"].CHE_PHAM_MAU_SUA])
                : !checkRole([ROLES["DANH_MUC"].CHE_PHAM_MAU_THEM])
            }
            layerId={layerId}
            allowDecimal={true}
          />
        );
      },
    },

    {
      key: 3,
      title: t("danhMuc.khoaChiDinhDv"),
      key: "khoaChiDinh",
      render: () => {
        return <KhoaChiDinh dichVuId={currentItem?.id} />;
      },
    },
    // {
    //   key: 5,
    //   title: "Phòng thực hiện",
    //   render: () => {
    //     return <PhongThucHien dichVuId={currentItem?.id} />;
    //   },
    // },
    {
      key: 4,
      title: t("danhMuc.nhomChiPhi"),
      render: () => {
        return <NhomChiPhi dichVuId={currentItem?.id} />;
      },
    },
    {
      key: 5,
      title: t("danhMuc.tuyChonGia"),
      render: () => {
        return (
          <TuyChonGia
            hideDoiTuongKcb
            dichVuId={currentItem?.id}
            hideKhoaChiDinh={true}
            hideLoaiHinhThanhToan={true}
            hideDoiTuongTuoi={true}
          />
        );
      },
    },
  ];

  const handleClickedBtnAdded = () => {
    setEditStatus(false);
    props.updateData({
      currentItem: {},
    });
  };

  refClickBtnAdd.current = handleClickedBtnAdded;

  const handleCollapsePane = () => {
    setCollapseStatus(!collapseStatus);
  };

  const handleChangeshowTable = () => {
    setState({
      changeShowFullTbale: true,
      showFullTable: !state.showFullTable,
    });
    setTimeout(() => {
      setState({
        changeShowFullTbale: false,
      });
    }, 1000);
  };

  return (
    <Main>
      <HomeWrapper
        title={t("danhMuc.danhMuc")}
        listLink={[
          { title: t("danhMuc.danhMuc"), link: "/danh-muc" },
          {
            title: t("khoMau.chePhamMau"),
            link: "/danh-muc/che-pham-mau",
          },
        ]}
      >
        <Col
          {...(!state.showFullTable
            ? collapseStatus
              ? TABLE_LAYOUT_COLLAPSE
              : TABLE_LAYOUT
            : null)}
          span={state.showFullTable ? 24 : null}
          className={`pr-3 ${
            state.changeShowFullTbale ? "" : "transition-ease"
          }`}
        >
          <DichVuKyThuat
            setEditStatus={setEditStatus}
            loaiDichVu={ID_LOAI_DICH_VU}
            classNameRow={"custom-header"}
            styleMain={{ marginTop: 0 }}
            styleContainerButtonHeader={{
              display: "flex",
              width: "100%",
              justifyContent: "flex-end",
              alignItems: "center",
              paddingRight: 35,
            }}
            title={t("danhMuc.danhMucChePhamMau")}
            onImport={onImportDMChePhamMau}
            onExport={() =>
              onExport({
                id: ID_LOAI_DICH_VU,
                ten: t("danhMuc.danhMucChePhamMau"),
              })
            }
            onImportDichVuKemTheo={onImportDichVuKemTheo}
            onExportDichVuKemTheo={onExportDichVuKemTheo}
            onImportTuyChonGia={onImportTuyChonGia}
            onExportTuyChonGia={onExportTuyChonGia}
            hiddenExportDichVuDayCong={true}
            buttonHeader={
              checkRole([ROLES["DANH_MUC"].CHE_PHAM_MAU_THEM])
                ? [
                    {
                      content: (
                        <Button
                          type="success"
                          onClick={handleClickedBtnAdded}
                          rightIcon={<SVG.IcAdd />}
                        >
                          {t("common.themMoiF1")}
                        </Button>
                      ),
                    },
                    {
                      className: `btn-change-full-table ${
                        state.showFullTable ? "small" : "large"
                      }`,
                      title: state.showFullTable ? (
                        <SVG.IcShowThuNho />
                      ) : (
                        <SVG.IcShowFull />
                      ),
                      onClick: handleChangeshowTable,
                    },
                    {
                      className: "btn-collapse",
                      title: collapseStatus ? (
                        <SVG.IcExtend />
                      ) : (
                        <SVG.IcCollapse />
                      ),
                      onClick: handleCollapsePane,
                    },
                  ]
                : [
                    {
                      className: `btn-change-full-table ${
                        state.showFullTable ? "small" : "large"
                      }`,
                      title: state.showFullTable ? (
                        <SVG.IcShowThuNho />
                      ) : (
                        <SVG.IcShowFull />
                      ),
                      onClick: handleChangeshowTable,
                    },
                    {
                      className: "btn-collapse",
                      title: collapseStatus ? (
                        <SVG.IcExtend />
                      ) : (
                        <SVG.IcCollapse />
                      ),
                      onClick: handleCollapsePane,
                    },
                  ]
            }
            layerId={layerId}
          />
        </Col>
        {!state.showFullTable && (
          <Col
            {...(collapseStatus ? ADD_LAYOUT_COLLAPSE : ADD_LAYOUT)}
            className={`mt-3 ${
              state.changeShowFullTbale ? "" : "transition-ease"
            }`}
            style={
              state.isSelected
                ? { border: "2px solid #c1d8fd", borderRadius: 20 }
                : {}
            }
          >
            <MultiLevelTab
              ref={refTab}
              defaultActiveKey={1}
              listPanel={listPanel}
              isBoxTabs={true}
            ></MultiLevelTab>
          </Col>
        )}
      </HomeWrapper>
    </Main>
  );
};

const mapStateToProps = (state) => {
  const {
    dichVuKyThuat: { currentItem },
  } = state;

  return {
    currentItem,
  };
};
const mapDispatchToProps = ({
  dichVuKyThuat: {
    onSearch,
    onSizeChange,
    onSortChange,
    onChangeInputSearch,
    updateData,
  },
  duongDung: { getListAllDuongDung },
}) => ({
  onSearch,
  onSizeChange,
  onSortChange,
  onChangeInputSearch,
  updateData,
  getListAllDuongDung,
});

export default connect(mapStateToProps, mapDispatchToProps)(ServicesPack);
