import React, { useEffect, useState, memo, useMemo } from "react";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { MODE } from "utils/editor-utils";
import moment from "moment";
import { SVG } from "assets";
import { <PERSON><PERSON>, <PERSON>ltip, ModalTrinhKy } from "components";
import { t } from "i18next";
import { useSearchParams, useThietLap } from "hooks";
import { THIET_LAP_CHUNG, TRANG_THAI_KY } from "constants/index";
import { useLocation } from "react-router-dom";
import useSign from "./useSign.js";
import ModalPatientSign from "pages/editor/report/components/ModalPatientSign";
import classNames from "classnames";
import { CheckOutlined } from "@ant-design/icons";
import { message } from "antd";

//isKyTheoCot: dùng cho các bảng hardcode có nhiều chữ ký cùng cấp => hiển thị các chữ ký có cấp < chữ ký số hiện tại
//showPatientSign: hiển thị người ký

const ImageSign = (props) => {
  const { setSearchParams } = useSearchParams();
  const { mode, component, form, formChange } = props;
  const [showIconRemoveSign, setShowIconRemoveSign] = useState(false);
  const itemProps = component.props || {};
  const location = useLocation().pathname;
  const headless =
    location.indexOf("headless-editor") != -1 && window.self == window.top;

  const isImageSignColumn = useMemo(() => {
    if (
      location.indexOf("EMR_HSDD043.1") != -1 ||
      location.indexOf("EMR_HSDD015.4") != -1 ||
      location.indexOf("EMR_HSDD015.5") != -1
    ) {
      return true;
    }
    return false;
  }, [location]);

  const khongTruyenNbDotDieuTriId = useMemo(() => {
    if (
      ["EMR_BA551", "EMR_BA595"].some((key) => location.indexOf(key) !== -1)
    ) {
      return true;
    }
    return false;
  }, [location]);

  const hienThiKyVanTayGocPhai = useMemo(() => {
    if (["EMR_BA083"].some((key) => location.indexOf(key) !== -1)) {
      return true;
    }
    return false;
  }, [location]);

  const {
    onHuyKyPhieu,
    showBtnSign,
    isShowHuyKy,
    getUrlBase64,
    onKyPhieu,
    contentAlign,
    positionCa,
    checkShowbtnHuyKy,
    dataSign,
    checkPermissonSign,
    onTrinhKy,
    refModalPatientSign,
    refModalTrinhKy,
    renderTenChanKy,
    onTuChoiKyPhieu,
    isShowBtnTuChoiKy,
    renderImageSign,
    renderLabelThoiGianKy,
    tenNguoiKyHoacTrinhKy,
    isShowHuyTrinhKy,
  } = useSign({
    itemProps,
    form,
    mode,
    formChange,
    component,
    headless,
    disabled: itemProps.disabled,
    khongTruyenNbDotDieuTriId,
    hienThiKyVanTayGocPhai,
    setSearchParams,
  });
  const {
    component: { init },
  } = useDispatch();

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };

  const handleShowIconRemove = () => {
    setShowIconRemoveSign(!showIconRemoveSign);
  };

  const onKyPhieu2 = () => {
    if (itemProps.isCheckKyBacSi) {
      message.error(t("editor.taiKhoanKyKhongPhaiLaTaiKhoanBacSi"));
      return;
    }
    //truyền lichSuKyId hiện tại vào function ký để tránh sau khi lưu dữ liệu xong thì mất lichSuKyId
    onKyPhieu({
      lichSuKyId:
        itemProps?.dataSign?.lichSuKyId || itemProps?.dataSign?.lichSuKy?.id,
      kyAnhVanTay: itemProps.kyAnhVanTay,
    });
  };
  return (
    <Main
      onClick={handleFocus}
      itemProps={itemProps}
      contentAlign={contentAlign}
      positionCa={positionCa}
    >
      {mode === MODE.config && (
        <div className="sign-image">
          <Button disabled={itemProps.disabled} rightIcon={<SVG.IcEdit />}>
            {t("editor.Ky")}
          </Button>
        </div>
      )}
      {mode !== MODE.config && (
        <div
          className={classNames("sign-image", {
            column: isImageSignColumn,
          })}
          onClick={handleShowIconRemove}
        >
          {dataSign ? (
            <div className="box-sign">
              <div className="info-sign">
                {checkShowbtnHuyKy({
                  isShowHuyKy,
                  lichSuKyId: dataSign?.id,
                  showIconRemoveSign,
                  headless: headless,
                }) && (
                  <Tooltip title={t("editor.huyKy")}>
                    <SVG.IcCloseCircle
                      color={"var(--color-red-primary)"}
                      onClick={onHuyKyPhieu}
                      className="btn-huyKy"
                    />
                  </Tooltip>
                )}

                {renderImageSign}

                {(itemProps.showPatientSign !== false ||
                  ([
                    TRANG_THAI_KY.TRINH_KY,
                    TRANG_THAI_KY.TRINH_KY_USB_TOKEN,
                  ].includes(dataSign.trangThai) &&
                    dataSign.nguoiKyId)) &&
                dataSign?.trangThai ? (
                  <div className="sign-ca">
                    {tenNguoiKyHoacTrinhKy}
                    {!itemProps.anThoiGianKy && dataSign.thoiGianKy && (
                      <div
                        style={{ textAlign: itemProps.canLeNguoiKy || "left" }}
                      >
                        {!itemProps.anNhanTenVaThoiGian
                          ? `${renderLabelThoiGianKy(dataSign?.loaiKy)}: `
                          : ""}
                        {moment(dataSign.thoiGianKy)
                          .utcOffset("+0700")
                          .format("HH:mm:ss DD/MM/YYYY")}
                      </div>
                    )}
                  </div>
                ) : (
                  ""
                )}
              </div>
              {itemProps.showCa &&
                // dataSign?.loaiKy !== 2 &&
                dataSign?.trangThai >= 50 && (
                  <CA
                    loaiKy={dataSign?.loaiKy}
                    getUrlBase64={getUrlBase64}
                  ></CA>
                )}
              {isShowHuyTrinhKy && (
                <Button
                  onClick={onTuChoiKyPhieu(true)}
                  type={"error"}
                  rightIcon={<SVG.IcCancel />}
                  size="small"
                >
                  {t("editor.huyTrinhKy")}
                </Button>
              )}
            </div>
          ) : (
            <>
              {showBtnSign && !headless && (
                <Button
                  onClick={checkPermissonSign ? onKyPhieu2 : onTrinhKy}
                  rightIcon={itemProps.hideIconSign ? null : <SVG.IcEdit />}
                  iconHeight={16}
                >
                  {renderTenChanKy}
                </Button>
              )}

              {isShowBtnTuChoiKy && (
                <Button
                  onClick={onTuChoiKyPhieu(false)}
                  type={"error"}
                  rightIcon={<SVG.IcCancel />}
                >
                  {t("phieuIn.tuChoiKy")}
                </Button>
              )}
            </>
          )}
        </div>
      )}
      <ModalPatientSign ref={refModalPatientSign} />
      <ModalTrinhKy ref={refModalTrinhKy}></ModalTrinhKy>
    </Main>
  );
};

const CA = memo(({ loaiKy, getUrlBase64 }) => {
  const [ca, setCa] = useState("");
  const [ANH_CA_KY, isFinishKy] = useThietLap(THIET_LAP_CHUNG.ANH_CA_KY);
  const [ANH_CA_KY_DIEN_TU, isFinishDienTu] = useThietLap(
    THIET_LAP_CHUNG.ANH_CA_KY_DIEN_TU
  );

  const [dataHIEN_THI_KY_SO_NB] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_KY_SO_NB,
    "FALSE"
  );

  const linkAnhCa = useMemo(() => {
    //ký số: 0, ký điện tử: 1
    if (loaiKy == 1 && isFinishDienTu) {
      return ANH_CA_KY_DIEN_TU;
    }
    if (isFinishKy) {
      return ANH_CA_KY;
    }
    return null;
  }, [loaiKy, isFinishKy, isFinishDienTu]);

  useEffect(() => {
    if (linkAnhCa) {
      getCa(linkAnhCa);
    }
  }, [linkAnhCa]);

  const getCa = async (url) => {
    const image = await getUrlBase64(url);
    setCa(image);
  };
  if (dataHIEN_THI_KY_SO_NB.eval() && loaiKy == 2) {
    return (
      <div className="ca-valid">
        <div>
          {t("editor.chuKySoHopLe")} <CheckOutlined />
        </div>
      </div>
    );
  } else {
    return ca ? <img className="image-ca" src={ca} alt=""></img> : null;
  }
});
ImageSign.propTypes = {};

export default ImageSign;
