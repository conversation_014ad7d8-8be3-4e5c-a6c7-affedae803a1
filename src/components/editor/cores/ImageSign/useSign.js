import { message } from "antd";
import { refUnCheckChangeFile } from "components/Prompt";
import { useConfirm, useListAll, useStore, useThietLap } from "hooks";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import { get, findLast } from "lodash";
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { MODE } from "utils/editor-utils";
import fileUtils from "utils/file-utils";
import { t } from "i18next";
import { refOnsaveData } from "pages/editor/report";
import { refValues } from "pages/editor/report/components/File";
import { EMR2Context, useEditor } from "components/editor/config";
import stringUitls from "mainam-react-native-string-utils";
import { ReplicationRowContext } from "../Table/ReplicationRow/Row";
import {
  LIST_PHIEU_IN_BANG_KE,
  LIST_PHIEU_IN_TRUYEN_THEM_KHOA_CHI_DINH,
  LIST_PHIEU_KY_CUSTOM,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_KY,
} from "constants/index";
import moment from "moment";
import { useLocation } from "react-router-dom";
import { checkRole } from "lib-utils/role-utils";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import { toSafePromise } from "lib-utils";
import { tenVietTat } from "utils";
const ref = React.createRef();
const FILE_PREFIX = "api/his/v1";

function useSign({
  form,
  itemProps,
  mode,
  formChange,
  component,
  editorId: currentEditorId,
  headless,
  disabled,
  khongTruyenNbDotDieuTriId = false,
  hienThiKyVanTayGocPhai = false,
  setSearchParams,
}) {
  const [dataSign, setDataSign] = useState();
  const [dsQuyenKy, setDsQuyenKy] = useState([]);
  const {
    nbDotDieuTriId,
    chuKySo,
    khoaChiDinhId,
    maPhieuKy,
    ngoaiTru,
    lichSuKyId,
    ma,
  } = getAllQueryString();
  const { search } = useLocation();
  const { showAsyncConfirm } = useConfirm();
  const { rowKey, setRowSign } = useContext(ReplicationRowContext) || {};
  const { id } = useParams();
  const refTimout = useRef();

  const [dataQUYEN_HUY_KY_CUA_CHINH_TAI_KHOAN_KY] = useThietLap(
    THIET_LAP_CHUNG.QUYEN_HUY_KY_CUA_CHINH_TAI_KHOAN_KY
  );
  const [dataHIEN_THI_KY_SO_NB] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_KY_SO_NB,
    "FALSE"
  );

  const [listAllQuyenKy] = useListAll(
    "quyenKy",
    {
      active: true,
      size: "",
      page: "",
    },
    true
  );
  const { editorId: editorIdFromContext } = useContext(EMR2Context);

  const editorId = useMemo(() => {
    return currentEditorId || editorIdFromContext;
  }, [editorIdFromContext, currentEditorId]);

  const queries = useMemo(() => {
    const queries = {};
    search
      .substring("1")
      .split("&")
      .forEach((item) => {
        const arr = item.split("=");
        let value = arr[1];
        if (
          arr[0].toLowerCase().includes("thoigian") &&
          moment(+arr[1]).isValid()
        ) {
          value = moment(+arr[1]).format("DD-MM-YYYY HH:mm:ss");
        }
        queries[arr[0]] = value;
      });
    return queries;
  }, [search]);

  const file = useEditor(editorId, "file", "");
  const chanKyBaoCao = useEditor(editorId, "chanKyBaoCao", "");
  const nhanVienId = useStore("auth.auth.nhanVienId", null);
  const dsVaiTroId = useStore("auth.auth.dsVaiTroId", []);
  const dsPhieuKy = useStore("phieuIn.dsPhieuKy", []);
  const refModalPatientSign = useRef();
  const refModalTrinhKy = useRef();

  const {
    phieuIn: {
      kyPhieu,
      kyPhieuTheoAPI,
      huyKyPhieu,
      trinhKy,
      tuChoiKyPhieu,
      kyPhieuTheoMa,
    },
    files: { setSignStatus },
    signer: { getUserSignImage, getImageSignPadPatient },
    thietLapQuyenKy: { getAll, getNhanVienByQuyenKy },
  } = useDispatch();

  useEffect(() => {
    if (!ref.current) ref.current = {};
    const uid = stringUitls.guid();
    ref.current[uid] = setDsQuyenKy;
    if (!window.isloadDsQuyen) {
      window.isloadDsQuyen = true;
      getAll({
        page: 0,
        sort: null,
      }).then((s) => {
        window.isloadDsQuyen = false;
        Object.keys(ref.current).forEach((item) => {
          if (item !== uid) {
            ref.current[item](s.data);
          }
        });
        setDsQuyenKy(s.data);
      });
    }
  }, []);

  const getPosition = (position) => {
    if (!position) return "flex-start";
    switch (itemProps.contentAlign) {
      case "left":
        return "flex-start";
      case "center":
        return "center";
      case "right":
        return "flex-end";
      default:
        return "flex-start";
    }
  };

  const onTuChoiKyPhieu = (isHuyKy) => () => {
    const lichSuKyId = form.lichSuKy?.id || thongTinPhieuKy.lichSuKyId;

    tuChoiKyPhieu({ lichSuKyId, isHuyKy }).then((s) => {
      setTimeout(() => {
        window.location.reload();
        setDataSign(null);
      }, 500);
    });
  };

  const fillApiTemplate = (template) => {
    const search = window.location.search.replace("?", "");
    let paramSearch = search.split("&");
    let objParams = paramSearch.reduce((a, b) => {
      const [key, value] = b.split("=");
      a[key] = value;
      return a;
    }, {});
    return template.replace(
      /{(\w+)}/g,
      (_, key) => (objParams[key] || form[key]) ?? ""
    );
  };

  const onHuyKyPhieu = async () => {
    const confirm =
      window.location.pathname.indexOf("headless-editor") != -1
        ? { action: "ok" }
        : await showAsyncConfirm({
            title: t("giayDayCong.xacNhanHuy"),
            content: t(
              `editor.banCoMuonHuy${dataSign?.loaiKy ? "KyDienTu" : "ChuKySo"}`
            ),
            cancelText: t("common.huy"),
            okText: t("common.xacNhan"),
            classNameOkText: "button-confirm",
            showBtnOk: true,
            typeModal: "warning",
          });

    if (confirm.action == "ok") {
      const lichSuKyId = form.lichSuKy?.id || thongTinPhieuKy.lichSuKyId;
      let data = {
        id: lichSuKyId,
        viTri: viTriIn,
        soPhieu: thongTinPhieuKy.soPhieu,
        chuKySo: +sttChuKy,
      };
      //nếu là ký điện tử
      if (dataSign?.loaiKy === 1) {
        if (
          itemProps.isDataForm &&
          get(form, itemProps.fieldName + "_nguoiKyId")
        ) {
          itemProps.removeSign();

          setDataSign(null);
          sendActionAutoSave();
          setTimeout(() => {
            window.location.reload();
          }, 500);
        } else {
          if (!data.id) {
            return;
          }
          huyKyPhieu(data).then((s) => {
            refUnCheckChangeFile.current && refUnCheckChangeFile.current();
            if (s.data) {
              setTimeout(() => {
                window.location.reload();
                setDataSign(null);
              }, 500);
            } else {
              setTimeout(() => {
                window.location.replace(
                  window.location.href.replace(`&lichSuKyId=${data.id}`, "")
                );
                setDataSign(null);
              }, 500);
            }
          });
        }
      } else {
        //nếu là ký số/ ký người bệnh
        if (!data.id) {
          return;
        }
        huyKyPhieu(data).then((s) => {
          setTimeout(() => {
            if (s.data) {
              window.location.reload();
            } else {
              window.location.replace(
                window.location.href.replace(`&lichSuKyId=${data.id}`, "")
              );
            }
            setDataSign(null);
          }, 500);
        });
      }
    }
  };

  const viTriIn = useMemo(() => {
    if (form?.takeMe) {
      //nếu chữ ký nằm trong ReplicationRow => set vị trí theo index của bảng
      return parseInt(form?.index || 0) + 1;
    } else {
      return itemProps.viTri || 1;
    }
  }, [itemProps?.viTri, form]);

  const sttChuKy = useMemo(() => {
    return itemProps.capKy || (itemProps.fieldName || "").replace("chuKy", "");
  }, [itemProps]);

  const tenChanKy = useMemo(() => {
    return get(chanKyBaoCao, `dsTenChanKy${sttChuKy}[0]`, "");
  }, [chanKyBaoCao, sttChuKy]);

  const thongTinChanKy = useMemo(() => {
    return {
      tenChanKy: get(chanKyBaoCao, `dsTenChanKy${sttChuKy}[0]`, ""),
      loaiKy: get(chanKyBaoCao, `loaiKy${sttChuKy}`, 1),
      quyenKyId: get(chanKyBaoCao, `quyenKy${sttChuKy}Id`, ""),
      soCapKy: get(chanKyBaoCao, `soCapKy`, 1),
      quyenKy: get(chanKyBaoCao, `quyenKy${sttChuKy}`, {}),
      viTriCa: get(chanKyBaoCao, `viTriCa${sttChuKy}`, ""),
      chuKySo: sttChuKy,
    };
  }, [chanKyBaoCao, sttChuKy]);

  const thongTinPhieuKy = useMemo(() => {
    let _thongTin = itemProps.lichSuKy || form?.lichSuKy;

    //lấy thông tin từ lịch sử ký
    if (_thongTin) {
      _thongTin.lichSuKyId = _thongTin.id;
      _thongTin.ma = maPhieuKy || ma || file?.ma;
    }
    //nếu ko tồn tại thì lấy từ dsPhieuKy của api phieu-in theo maManHinh/ maViTri
    if (!_thongTin) {
      _thongTin =
        (dsPhieuKy || []).find(
          (x) =>
            x.soPhieu == itemProps?.dataSign?.soPhieu ||
            x.soPhieu == form?.soPhieu
        ) || {};
    }

    //nếu là tờ điều trị ngoại trú ma=EMR_BA077 P184 và ngoaiTru=true
    //=> lấy thông tin soPhieu theo bản ghi khám
    if (maPhieuKy == "P184" && ngoaiTru) {
      if (!form?.lichSuKy && _thongTin) {
        _thongTin.soPhieu = itemProps?.dataSign?.soPhieu;
        _thongTin.lichSuKyId = null;
      }
    }
    return _thongTin || {};
  }, [dsPhieuKy, itemProps, form, file]);

  const isBangKe = useMemo(
    () => LIST_PHIEU_IN_BANG_KE.includes(thongTinPhieuKy.ma || queries?.ma),
    [thongTinPhieuKy?.ma, queries?.ma]
  );

  const isTruyenThemKhoaChiDinh = useMemo(
    () => LIST_PHIEU_IN_TRUYEN_THEM_KHOA_CHI_DINH.includes(maPhieuKy),
    [maPhieuKy]
  );

  const checkPermissonSign = useMemo(() => {
    const thietLapQuyenKy = dsQuyenKy.filter(
      (item) => item.quyenKyId === chanKyBaoCao[`quyenKy${sttChuKy}Id`]
    );

    const permission =
      thietLapQuyenKy.find(
        (el) =>
          el.nhanVienId === nhanVienId &&
          (el.khoaChiDinhId
            ? el.khoaChiDinhId == thongTinPhieuKy.khoaChiDinhId
            : true) &&
          (el.khoaThucHienId
            ? el.khoaThucHienId == thongTinPhieuKy.khoaThucHienId
            : true)
      ) ||
      thietLapQuyenKy.find((el) =>
        (dsVaiTroId || []).some(
          (vaiTro) =>
            vaiTro === el.vaiTroId &&
            (el.khoaChiDinhId
              ? el.khoaChiDinhId == thongTinPhieuKy.khoaChiDinhId
              : true) &&
            (el.khoaThucHienId
              ? el.khoaThucHienId == thongTinPhieuKy.khoaThucHienId
              : true)
        )
      );
    // Nếu loại ký là ký số hoặc ký điện tử  thì sẽ check quyền ký của tài khoản còn ký người bệnh mặc định sẽ có quyền
    return thongTinChanKy?.loaiKy === 2 ? true : permission;
  }, [thongTinChanKy, dsQuyenKy, thongTinPhieuKy, dsVaiTroId, form]);

  const showBtnSign = useMemo(() => {
    if (disabled) return false;
    if (itemProps.alwaysShow) {
      return true;
    } else if (form?.takeMe) {
      //case ReplicationRow
      //nếu là chữ ký số 1 thì luôn hiển thị
      if (sttChuKy === 1) return true;
      //nếu là chữ ký số 2 trở đi => tìm chữ ký số trước đó tương ứng theo vị trí in
      //nếu trạng thái = 50 => hiển thị nút in
      if (sttChuKy > 1) {
        return (
          (form?.lichSuKy?.dsChuKy || []).find(
            (item) => item.chuKySo === sttChuKy - 1
          )?.trangThai === 50
        );
      }

      return false;
    } else if (sttChuKy) {
      return (
        sttChuKy == (form?.lichSuKy?.chuKySo || 1) ||
        // Mặc định chữ ký số 1 luôn hiện

        (form?.lichSuKy?.dsChuKy || []).find((item) => item.chuKySo == sttChuKy)
          ?.trangThai == TRANG_THAI_KY.TRINH_KY ||
        (form?.lichSuKy?.dsChuKy || []).find((item) => item.chuKySo == sttChuKy)
          ?.trangThai == TRANG_THAI_KY.TRINH_KY_USB_TOKEN ||
        // Chữ ký đang ở trạng thái chờ ký
        (sttChuKy === form?.lichSuKy?.chuKySo + 1 &&
          form?.lichSuKy?.dsChuKy.find(
            (item) => item.chuKySo === form?.lichSuKy?.chuKySo
          )?.trangThai === 50) ||
        // Chữ ký trc đã hoàn thành và chữ ký tiếp theo k ở trạng thái trình ký

        (itemProps.isKyTheoCot && sttChuKy < (form?.lichSuKy?.chuKySo || 1)) ||
        //Trường hợp được thiết lập check không cấp ký thì chân ký trước đó vẫn được ký
        (sttChuKy === form?.lichSuKy?.chuKySo - 1 &&
          form?.lichSuKy?.[`dieuKienKy${form?.lichSuKy?.chuKySo}`]?.includes(
            20
          )) ||
        chanKyBaoCao?.[`dieuKienKy${sttChuKy}`]?.includes(20)
      );
    }

    return false;
  }, [sttChuKy, form, itemProps, viTriIn, chanKyBaoCao, disabled]);
  const isShowHuyKy = useMemo(() => {
    let thongTinKy = get(form, "lichSuKy", {});
    const thongTinChuKySo = (thongTinKy?.dsChuKy || []).find(
      (el) => el.chuKySo == sttChuKy
    );

    if (
      (thongTinChanKy?.loaiKy === 2 && !isBangKe) ||
      itemProps.isMultipleSign
    ) {
      return itemProps.allowReset;
    } else if (form?.takeMe) {
      //case ReplicationRow
      //tìm chữ ký có chung vị trí và trạng thái đã ký
      const dsChuKySoTheoViTri = (form?.lichSuKy?.dsChuKy || [])
        .filter((item) => item.viTri === viTriIn && item.trangThai)
        .map((item) => item.chuKySo);
      return (
        sttChuKy == Math.max(...dsChuKySoTheoViTri) &&
        itemProps.allowReset &&
        (dataQUYEN_HUY_KY_CUA_CHINH_TAI_KHOAN_KY.eval()
          ? checkRole([ROLES["EDITOR"].HUY_KY_CHU_KY_CHINH_TK_KY])
          : true)
      );
    } else {
      // Chỉ hiển thị nút hủy ký với chữ ký cuối cùng
      const chuKyCuoiCung =
        findLast(thongTinKy?.dsChuKy || [], (item) => item.trangThai >= 50) ||
        {};

      // Nếu config cho phép hủy
      return (
        itemProps.allowReset &&
        chuKyCuoiCung?.chuKySo == sttChuKy &&
        (dataQUYEN_HUY_KY_CUA_CHINH_TAI_KHOAN_KY.eval()
          ? checkRole([ROLES["EDITOR"].HUY_KY_CHU_KY_CHINH_TK_KY])
          : true)
      );
    }
  }, [
    thongTinChanKy,
    form,
    viTriIn,
    nhanVienId,
    dataQUYEN_HUY_KY_CUA_CHINH_TAI_KHOAN_KY,
  ]);
  const getUrlBase64 = useCallback(async (urlImage) => {
    let url = await fileUtils.getFromUrl({
      url: fileUtils.absoluteFileUrl(urlImage),
    });
    url = btoa(
      new Uint8Array(url).reduce(
        (data, byte) => data + String.fromCharCode(byte),
        ""
      )
    );

    url = "data:image/png;base64," + url;
    return url;
  }, []);

  const sign = async ({
    formId,
    soPhieu,
    duLieuKy,
    lichSuKyId,
    kyAnhVanTay,
    reloadSauKy = true,
  }) => {
    if (mode == "config") {
      message.error(t("editor.khongKyTrongCheDoCaiDat"));
      return;
    }
    if (!form) return;
    const idPhieu = id || itemProps?.dataSign?.id || form.soPhieu;
    //ký người bệnh
    if (thongTinChanKy?.loaiKy === 2) {
      const kyNguoiBenh = async ({
        anhKy,
        soCanCuoc,
        fileChuyenDoi,
        tenNguoiKy,
        nbDotDieuTriId,
        soDienThoai,
        reloadSauKy,
      }) => {
        let res = {};
        if (thongTinPhieuKy.lichSuKyId) {
          res = await kyPhieu({
            anhKy,
            soCanCuoc,
            id: thongTinPhieuKy.lichSuKyId,
            chuKySo: +sttChuKy,
            viTri: viTriIn,
            idPhieu: idPhieu,
            api: file.api || itemProps?.dataSign?.api,
            isEditor: true,
            suDungApiKySoRieng: file?.cauHinh?.suDungApiKySoRieng,
            isBangKe,
            fileChuyenDoi,
            tenNguoiKy,
            soDienThoai,
          });
        } else {
          res = await kyPhieu({
            anhKy,
            soCanCuoc,
            chuKySo: +sttChuKy,
            baoCaoId: chanKyBaoCao?.baoCaoId,
            soPhieu:
              soPhieu ||
              form?.soPhieu ||
              thongTinPhieuKy?.soPhieu ||
              id ||
              idPhieu,
            nbDotDieuTriId,
            duLieu:
              duLieuKy || refValues.current || itemProps?.dataSign?.duLieu,
            viTri: viTriIn,
            idPhieu: formId || idPhieu,
            api: file.api || itemProps?.dataSign?.api,
            isBangKe,
            isEditor: true,
            suDungApiKySoRieng: file?.cauHinh?.suDungApiKySoRieng,
            fileChuyenDoi,
            tenNguoiKy,
            soDienThoai,
          });
        }
        if (reloadSauKy)
          setTimeout(() => {
            window.location.reload();
          }, 500);
        //nếu reload sau ký thì khỏi cần post message
        else {
          sendActionGetEditor();
          sendActionRefreshPhieuKy();
        }
      };
      let image = null;
      const _nbDotDieuTriId =
        nbDotDieuTriId ||
        itemProps?.dataSign?.nbDotDieuTriId ||
        form?.nbDotDieuTriId ||
        id;
      let res = null;

      if (_nbDotDieuTriId && kyAnhVanTay) {
        res = await toSafePromise(
          nbDotDieuTriProvider.tongTienDieuTri(_nbDotDieuTriId)
        );

        if (!res?.[0] && res?.[1]?.data?.anhVanTay) {
          res = await toSafePromise(
            fileUtils.urlToBase64(res[1].data.anhVanTay)
          );

          if (!res?.[0] && res?.[1]) image = res[1];
        }
      }
      if (!image) {
        res = await toSafePromise(getImageSignPadPatient());
        if (!res?.[0] && res?.[1]) {
          image = res?.[1];
        }
      }
      image =
        image && !image.includes("data:image/png;base64")
          ? `data:image/png;base64,${image}`
          : image;
      refModalPatientSign.current.show(
        {
          anhKy: image,
          tenNguoiKy: form?.tenNb,
          soDienThoai: form?.soDienThoai,
          tenNguoiBaoLanh: form?.tenNguoiBaoLanh,
          tenNguoiBaoLanh2: form?.tenNguoiBaoLanh2,
          tenMoiQuanHe: form?.tenMoiQuanHe,
          tenMoiQuanHe2: form?.tenMoiQuanHe2,
          hienThiKyVanTayGocPhai,
        },
        async ({
          image,
          soCanCuoc,
          tenNguoiKy,
          fileChuyenDoi,
          soDienThoai,
        }) => {
          const anhKy = (image || "").replace("data:image/png;base64,", "");
          kyNguoiBenh({
            soCanCuoc,
            anhKy,
            tenNguoiKy,
            nbDotDieuTriId: _nbDotDieuTriId,
            fileChuyenDoi,
            soDienThoai,
            reloadSauKy,
          });
        }
      );
      return;
    }

    let res;
    let isReloaded = false;
    if (
      LIST_PHIEU_KY_CUSTOM.includes(thongTinPhieuKy?.ma) &&
      (!thongTinPhieuKy?.lichSuKyId || thongTinPhieuKy?.chuKySo === 1)
    ) {
      //Chỉ xử lý custom với chữ ký đầu tiên
      res = await kyPhieuTheoMa({
        ma: thongTinPhieuKy?.ma,
        id: id || itemProps?.dataSign?.id,
      });
    } else if (itemProps?.api) {
      //check nếu có setting api thì call theo api đã thiết lập
      let api = itemProps?.api.replace("{ID}", id);
      api = fillApiTemplate(api);

      const payload = {
        api,
        id: thongTinPhieuKy?.lichSuKyId || itemProps?.dataSign?.lichSuKyId,
        chuKySo: thongTinChanKy?.chuKySo,
        baoCaoId: thongTinPhieuKy?.baoCaoId,
        viTri: viTriIn,
        soPhieu:
          soPhieu || form?.soPhieu || thongTinPhieuKy?.soPhieu || id || idPhieu,
        duLieu: refValues.current || itemProps?.dataSign?.duLieu,
      };
      res = await kyPhieuTheoAPI(payload);
    } else {
      if (
        itemProps.checkTrungNguoiKy &&
        (form.lichSuKy?.dsChuKy || []).find(
          (item) => item.chuKySo == sttChuKy && nhanVienId === item.nguoiKyId
        )
      ) {
        message.error(t("editor.taiKhoanDaKyChanKyNay"));
        return;
      }
      const _khoaChiDinhId = khoaChiDinhId || get(form, "khoaChiDinhId");
      if (
        lichSuKyId ||
        thongTinPhieuKy.lichSuKyId ||
        itemProps?.dataSign?.lichSuKyId ||
        itemProps?.dataSign?.lichSuKy?.id
      ) {
        //nếu ký chữ ký thứ 2 hoặc ký phiếu trình ký thì truyền thêm lichSuKyId
        res = await kyPhieu({
          id:
            thongTinPhieuKy.lichSuKyId ||
            itemProps?.dataSign?.lichSuKyId ||
            itemProps?.dataSign?.lichSuKy?.id ||
            lichSuKyId,
          chuKySo: +sttChuKy,
          loaiKy: thongTinChanKy?.loaiKy || itemProps.loaiKy,
          api: file.api || itemProps?.dataSign?.api,
          idPhieu,
          viTri: viTriIn,
          isBangKe,
          khoaChiDinhId: isTruyenThemKhoaChiDinh ? _khoaChiDinhId : null,
          baoCaoId: thongTinPhieuKy.baoCaoId,
          isEditor: true,
          suDungApiKySoRieng: file?.cauHinh?.suDungApiKySoRieng,
          soPhieu:
            soPhieu ||
            form?.soPhieu ||
            thongTinPhieuKy?.soPhieu ||
            id ||
            idPhieu,
        });
        if (setSearchParams) {
          setSearchParams({ t: Date.now() }, { setFilteredKeys: false });
        } else {
          //nếu có thiết lập hienThiDuLieuTaiThoiDiemKy thì cần reload lại phiếu để lấy dữ liệu tại thời điểm ký
          if (file?.cauHinh?.hienThiDuLieuTaiThoiDiemKy && reloadSauKy) {
            isReloaded = true; //đánh dấu xử lý reload
            setTimeout(() => {
              window.location.reload();
            }, 500);
          }
        }
      } else {
        res = await kyPhieu({
          baoCaoId: chanKyBaoCao?.baoCaoId,
          soPhieu:
            soPhieu ||
            form?.soPhieu ||
            thongTinPhieuKy?.soPhieu ||
            id ||
            idPhieu,
          ...(khongTruyenNbDotDieuTriId
            ? {}
            : {
                nbDotDieuTriId:
                  nbDotDieuTriId || id || itemProps?.dataSign?.nbDotDieuTriId,
              }),
          khoaChiDinhId: isTruyenThemKhoaChiDinh ? _khoaChiDinhId : null,
          khoaThucHienId: null,
          duLieu: refValues.current || itemProps?.dataSign?.duLieu,
          chuKySo: +sttChuKy,
          loaiKy: thongTinChanKy?.loaiKy || itemProps.loaiKy,
          api: file.api || itemProps?.dataSign?.api,
          idPhieu: formId ? formId : idPhieu,
          viTri: viTriIn,
          isBangKe,
          maBaoCao: itemProps?.dataSign?.maBaoCao,
          isEditor: true,
          suDungApiKySoRieng: file?.cauHinh?.suDungApiKySoRieng,
        });

        //=> khi ký chân đầu tiên thì cần update lại lichSuKyId cho những chân ký tiếp theo
        if (res.data?.id) {
          isReloaded = true; //đánh dấu xử lý reload, ko cần thực hiện action bên dưới nữa
          if (lichSuKyId != res.data?.id) {
            if (setSearchParams) {
              setSearchParams(
                { lichSuKyId: res.data?.id, t: Date.now() },
                { setFilteredKeys: false }
              );
            } else {
              setTimeout(() => {
                if (lichSuKyId) {
                  window.location.replace(
                    `${window.location.href.replace(
                      `&lichSuKyId=${lichSuKyId}`,
                      `&lichSuKyId=${res.data?.id}`
                    )}`
                  );
                } else {
                  window.location.replace(
                    `${window.location.href}${
                      window.location.search ? `&` : "?"
                    }lichSuKyId=${res.data?.id}`
                  );
                }
              }, 500);
            }
          }
        }
      }
    }
    //nam.mn 27/10/2025 hiện tại trước khi ký phiếu đã auto save rồi.
    //sendActionAutoSave();

    // sendActionGetEditor();

    refUnCheckChangeFile.current && refUnCheckChangeFile.current();
    // sendActionRefreshPhieuKy();
    if (reloadSauKy && !isReloaded && (itemProps.dataSign || form?.takeMe)) {
      setTimeout(() => {
        window.location.reload();
      }, 500);
    }
  };

  const getAnhKyByFieldName = async (nhanVienId) => {
    if (!nhanVienId) return;
    try {
      let s = {};
      s = await getUserSignImage(nhanVienId);

      const image = await fileUtils.getFromUrl({
        prefix: FILE_PREFIX,
        url: s.anhKy,
      });
      var base64 = btoa(
        new Uint8Array(image).reduce(
          (data, byte) => data + String.fromCharCode(byte),
          ""
        )
      );
      const base64Image = "data:image/png;base64," + base64;
      return {
        base64Image,
        anhKy: s.anhKy,
        tenNguoiKy: s.ten,
        tenVietTat: s.tenVietTat,
        vietTatHocHamHocVi: s.vietTatHocHamHocVi,
      };
    } catch (error) {
      return "";
    }
  };

  const onKyPhieu = async ({
    duLieuKy,
    lichSuKyId,
    kyAnhVanTay,
    reloadSauKy = false,
  } = {}) => {
    //SAKURA-75662 thêm check để hiển thị message
    if (itemProps?.kyAnhVanTay && !itemProps?.anhVanTay) {
      message.error(t("editor.nguoiBenhChuaCoDuLieuVanTay"));
      if (itemProps?.batBuocKyVanTay) {
        return;
      }
    }
    try {
      let res = {};
      if (refOnsaveData.current) {
        res = await refOnsaveData.current();

        await new Promise((r) => setTimeout(r, 3000));
      }
      if (!form.id) {
        //sau khi lưu phiếu mới thì lấy id response trả về để ký phiếu
        sign({
          formId: res?.id,
          soPhieu: res?.soPhieu,
          duLieuKy,
          lichSuKyId,
          kyAnhVanTay,
          reloadSauKy,
        });
      } else {
        sign({ duLieuKy, lichSuKyId, kyAnhVanTay, reloadSauKy });
      }
    } catch (error) {
      message.error(t("editor.kySoThatBaiXinVuiLongThuLaiSau"));
      console.error(error);
    }
  };

  const contentAlign = useMemo(() => {
    return getPosition(itemProps.contentAlign);
  }, [itemProps.contentAlign]);

  const positionCa = useMemo(() => {
    return getPosition(itemProps.viTriAnhCa);
  }, [itemProps.viTriAnhCa]);

  const checkShowbtnHuyKy = ({ isShowHuyKy, showIconRemoveSign, headless }) => {
    return isShowHuyKy && showIconRemoveSign && !headless;
  };

  const sendActionAutoSave = () => {
    if (itemProps.autoSave || itemProps.autoSave === undefined) {
      window.postMessage(
        {
          TYPE: "EDITOR-SIGNED",
        },
        window.location.origin
      );
    }
  };

  const sendActionGetEditor = () => {
    if (itemProps.autoSave || itemProps.autoSave === undefined) {
      window.postMessage(
        {
          TYPE: "RELOAD-PHIEU-EDITOR",
        },
        window.location.origin
      );
    }
  };

  const sendActionRefreshPhieuKy = () => {
    window.postMessage(
      {
        TYPE: "REFRESH-PHIEU-KY",
      },
      window.location.origin
    );
  };

  useEffect(() => {
    if (!form) {
      return;
    }

    if (refTimout.current) clearTimeout(refTimout.current);
    refTimout.current = setTimeout(
      ({ sttChuKy, checkPermissonSign, viTriIn }) => {
        hienThiChuKy({
          sttChuKy,
          checkPermissonSign,
          viTriIn,
        });
      },
      300,
      { sttChuKy, checkPermissonSign, viTriIn }
    );
  }, [
    Object.keys(form || {})?.length,
    form?.lichSuKy,
    sttChuKy,
    viTriIn,
    checkPermissonSign,
  ]);

  const hienThiChuKy = async ({ sttChuKy, checkPermissonSign, viTriIn }) => {
    const chuKy = get(form, "lichSuKy.dsChuKy", []).find(
      (item) => item.chuKySo == sttChuKy && viTriIn == (item.viTri || 1)
    );

    const conditionSetSign = () => {
      if (
        [TRANG_THAI_KY.TRINH_KY, TRANG_THAI_KY.TRINH_KY_USB_TOKEN].includes(
          chuKy?.trangThai
        ) &&
        !checkPermissonSign &&
        chuKy.nguoiKyId
      ) {
        // Nếu chữ ký trạng thái trình ký và không có quyền ký thì không hiển thị thông tin đã được trình ký
        return true;
      } else if (chuKy?.trangThai >= 50) {
        // nếu trạng thái đang ký hoặc đã hoàn thành ký thì hiển thị thông tin đã ký
        return true;
      } else {
        return false;
      }
    };
    if (conditionSetSign()) {
      try {
        let url = "";
        if (chuKy?.anhKy) {
          url = await getUrlBase64(chuKy?.anhKy);
        }
        setDataSign({ ...chuKy, url });
        let sign = {
          componentId: component?.key,
          block: component?.props?.disableIfSigned,
          levelSign: +sttChuKy,
          chuKy: url,
          viTri: viTriIn,
        };

        if (setRowSign) {
          setRowSign(rowKey, {
            block: component?.props?.disableIfSigned,
            viTriIn,
            chuKy: url,
          });
        } else {
          setSignStatus(editorId, sign);
        }
      } catch (error) {
        console.log("error", error);
      }
    } else {
      setDataSign(null);
    }
  };

  const checkTrangThaiKy = () => {
    hienThiChuKy({ form, sttChuKy, checkPermissonSign, viTriIn });
  };

  const onTrinhKy = async () => {
    //SAKURA-75662 thêm check để hiển thị message
    if (itemProps?.kyAnhVanTay && !itemProps?.anhVanTay) {
      message.warning(t("edior.nguoiBenhChuaCoDuLieuVanTay"));
      if (itemProps?.batBuocKyVanTay) {
        return;
      }
    }
    if (itemProps.isCheckKyBacSi) {
      message.error(t("editor.taiKhoanKyKhongPhaiLaTaiKhoanBacSi"));
      return;
    }
    const res = await getNhanVienByQuyenKy({
      quyenKyId: chanKyBaoCao[`quyenKy${sttChuKy}Id`],
    });
    const data = {
      dsTaiKhoan: res,
      dsQuyenKy: listAllQuyenKy,
      quyenKyId: chanKyBaoCao[`quyenKy${sttChuKy}Id`],
    };
    refModalTrinhKy.current &&
      refModalTrinhKy.current.show(data, async ({ nguoiKyId }) => {
        let res = {};
        if (refOnsaveData.current) {
          res = await refOnsaveData.current();
        }
        trinhKy({
          baoCaoId: thongTinPhieuKy.baoCaoId || chanKyBaoCao?.baoCaoId,
          soPhieu: thongTinPhieuKy.soPhieu || res.soPhieu,
          nbDotDieuTriId:
            nbDotDieuTriId || id || itemProps?.dataSign?.nbDotDieuTriId,
          khoaChiDinhId: null,
          khoaThucHienId: null,
          duLieu: refValues.current,
          chuKySo: +sttChuKy,
          loaiKy: thongTinChanKy?.loaiKy || itemProps.loaiKy,
          api: file.api,
          id: thongTinPhieuKy?.lichSuKyId,
          viTri: viTriIn,
          nguoiKyId,
        });
      });
  };

  const renderTenChanKy = useMemo(() => {
    let strings = "";
    if (itemProps.customText) {
      strings = checkPermissonSign ? itemProps.customText : t("editor.trinhKy");
    } else if (thongTinChanKy.loaiKy === 2) {
      //loại ký nb
      strings = `${t("editor.Ky")} ${itemProps.textSignPatient || ""}`;
    } else {
      if (checkPermissonSign) {
        strings = `${t("editor.xacNhanKy")} ${thongTinChanKy?.tenChanKy || ""}`;
      } else {
        strings = `${t("editor.trinhKy")} ${thongTinChanKy?.tenChanKy || ""}`;
      }
    }

    return strings;
  }, [checkPermissonSign, tenChanKy, itemProps]);

  const isShowBtnTuChoiKy = useMemo(() => {
    if (itemProps.hideTuChoiKy || disabled) {
      return false;
    }
    // Nếu là ký điện tử và là ký người bệnh thì mặc định ẩn
    // Nếu không có quyền ký thì cũng ấn
    if (thongTinChanKy?.loaiKy === 2 || !checkPermissonSign) {
      return false;
    } else if (thongTinChanKy?.chuKySo) {
      //Nếu là chữ ký đầu tiên (sttChuKy = 1) => kiểm tra trạng thái ở thông tin phiếu ký
      if (thongTinChanKy?.chuKySo == (form?.lichSuKy?.chuKySo || 1)) {
        return [
          TRANG_THAI_KY.TRINH_KY_USB_TOKEN,
          TRANG_THAI_KY.TRINH_KY,
        ].includes(thongTinPhieuKy.trangThai);
      }
      //Nếu là chữ ký thứ 2 trở đi => lấy trong lichSuKy
      const _lichSuKy = (form?.lichSuKy?.dsChuKy || []).find(
        (item) => item.chuKySo == thongTinChanKy.chuKySo
      );
      return [
        TRANG_THAI_KY.TRINH_KY_USB_TOKEN,
        TRANG_THAI_KY.TRINH_KY,
      ].includes(_lichSuKy?.trangThai);
    }

    return false;
  }, [checkPermissonSign, thongTinChanKy, form, itemProps, disabled]);

  const renderImageSign = useMemo(() => {
    if (itemProps.showTenVietTat) {
      return (
        <div className="text-name-patient-sign">
          {dataSign?.tenVietTat || tenVietTat(dataSign?.tenNguoiKy || "") || ""}
        </div>
      );
    }
    if (
      !itemProps.showTenVietTat &&
      dataSign?.url &&
      dataSign.trangThai >= 20
    ) {
      return (
        <div className="image-sign">
          <img className="text-patient-sign" src={dataSign.url} alt=""></img>
          {itemProps.showTenVietTatDuoiChuKy && (
            <div className="text-short-name-patient">
              {dataSign?.tenVietTat ||
                tenVietTat(dataSign?.tenNguoiKy || "") ||
                ""}
            </div>
          )}
        </div>
      );
    }
    return "";
  }, [dataSign, itemProps]);

  const renderLabelThoiGianKy = (loaiKy) => {
    switch (loaiKy) {
      case 0:
        return t("editor.ngayKySo");
      case 1:
        return t("editor.ngayKyDienTu");
      case 2:
        return t("kySo.thoiGianKy");
      default:
        return t("editor.ngayKySo");
    }
  };

  const tenNguoiKyHoacTrinhKy = useMemo(() => {
    const isKyNb = dataSign?.loaiKy === 2;

    const isTrinhKyStatus =
      [TRANG_THAI_KY.TRINH_KY, TRANG_THAI_KY.TRINH_KY_USB_TOKEN].includes(
        dataSign?.trangThai
      ) && !!dataSign?.nguoiKyId;

    const roleKey = isTrinhKyStatus
      ? "editor.dangTrinhKy"
      : `editor.${
          isKyNb ? "nguoiKy" : dataSign?.loaiKy ? "nguoiKyDienTu" : "nguoiKySo"
        }`;

    const hocHamHocVi = !isKyNb ? dataSign?.vietTatHocHamHocVi || "" : "";

    const tenNguoiKy = dataSign?.tenNguoiKy || "";
    const textAlign = itemProps?.canLeNguoiKy ?? "left";

    return (
      <div style={{ textAlign }}>
        {!itemProps.anNhanTenVaThoiGian && `${t(roleKey)}: `}
        {hocHamHocVi && `${hocHamHocVi} `}
        {tenNguoiKy}
      </div>
    );
  }, [dataSign, dataHIEN_THI_KY_SO_NB, itemProps]);

  const isShowHuyTrinhKy = useMemo(() => {
    return (
      [TRANG_THAI_KY.TRINH_KY, TRANG_THAI_KY.TRINH_KY_USB_TOKEN].includes(
        dataSign?.trangThai
      ) && itemProps.huyTrinhKy
    );
  }, [dataSign, itemProps]);

  return {
    //variables
    sttChuKy,
    tenChanKy,
    isShowHuyKy,
    isShowBtnTuChoiKy,
    checkShowbtnHuyKy,
    showBtnSign,
    dataSign,
    checkPermissonSign,
    thongTinPhieuKy,
    positionCa,
    tenNguoiKyHoacTrinhKy,
    isShowHuyTrinhKy,

    //func
    onKyPhieu,
    onHuyKyPhieu,
    onTrinhKy,
    onTuChoiKyPhieu,
    renderLabelThoiGianKy,

    getPosition,
    getUrlBase64,
    getAnhKyByFieldName,
    contentAlign,
    setDataSign,

    refModalPatientSign,
    refModalTrinhKy,
    renderTenChanKy,
    renderImageSign,
    checkTrangThaiKy,
  };
}

export default useSign;
