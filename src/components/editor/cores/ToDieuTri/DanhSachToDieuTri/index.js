import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { GlobalStyle, Main, NgayDieuTriStyled } from "./styled";
import {
  ENUM,
  LOAI_DICH_VU,
  DS_TINH_CHAT_KHOA,
  ROLES,
  THIET_LAP_CHUNG,
} from "constants/index";
import { Button, Col, Menu, message, Row } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { Popover, Dropdown, TextField } from "components";
import { useConfirm, useEnum, useListAll, useStore, useThietLap } from "hooks";
import moment from "moment";
import { DeboundInput } from "components/editor/config";
import DropDownList from "../../DropDownList";
import { SVG } from "assets";
import { PlusOutlined, CheckOutlined } from "@ant-design/icons";
import {
  formatText,
  RenderBangThongSo,
  renderListDv,
  renderListDvNgoaiTru,
  renderMau,
  renderThong<PERSON>o<PERSON>hung,
  <PERSON><PERSON><PERSON>h,
  parseTextToHtml,
  DS_TIEU_DE_PHAI,
  renderThongTinChung,
  findClosestChiSoSong,
  RenderThuocs,
  TEN_TIEU_DE_TRAI_BY_FIELDNAME,
  TEN_TIEU_DE_TRAI_BY_FIELDNAME_SAN_HN,
} from "./ultils";
import { DownOutlined } from "@ant-design/icons";
import { MODE, stripHtml } from "utils/editor-utils";
import { get, groupBy, orderBy } from "lodash";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { SettingOutlined } from "@ant-design/icons";
import ImageSign from "../../ImageSign";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import { useParams, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { renderDienBienNgoaiTru } from "./renderDienBienNgoaiTru";
import { renderThemMoiToDieuTriNgoaiTru } from "./renderThemMoiToDieuTriNgoaiTru";
import DienBienSanKhoa from "./sanKhoa/DienBienSanKhoa";
import YLenhSanKhoa from "./sanKhoa/YLenhSanKhoa";
import MainDienBienSoSinh from "./soSinh/DienBienSoSinh";
import YLenhSoSinh from "./soSinh/YLenhSoSinh";
import Barcode from "../../Barcode";
import { EMR2Context, useEditor } from "components/editor/config";

const DanhSachToDieuTri = (
  {
    itemProps,
    form,
    onFormChange = () => {},
    block,
    toDieuTri = {},
    toDieuTris,
    mode,
    isDisable = true,
    dsChiSoSong,
    listCss,
    khoaPhieuSauKhiKy = false,
    showDuongDungThuoc,
    hienSoLuongDvt,
    hienThuocNhaThuoc,
    hienThuocKeNgoai,
    hienThuocTuVan,
    hienThiSLBuoi,
    hienThiTocDoTruyen,
    hienThiDonViTocDoTruyen,
    hienBSDieuTri,
    hienLoaiChiDinh,
    listSapXepYLenh,
    listSapXepDienBienBenh,
    dsToDieuTri,
    onAddToDieuTri,
    onDeleteToDieuTri,
    gopDv,
    tuyChinhTruongSapXepYLenh,
    hienThiChanKyTruongKhoa = false,
    hienThiTenTruongKhoa,
    hienThiGio = true,
    hienThiTenDayDu,
    gopToDieuTriNhieuNgay,
    listToDieuTriByNb = [],
    hienThiDvktNgungYLenhKhongThucHien = false,
    hienThiDvKhoNgungYLenh = false,
    hienThiSoLuongYeuCauDvKho = false,
    hienThiTextTrangThaiHoanNgungYLenhKhongThucHienDvkt = false,
    hienThiTextTrangThaiHoanNgungYLenhDvKho = false,
    ...props
  },
  refs
) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const [state, _setState] = useState({
    showBoSung: [],
    showBangThongSo: [],
    showChiSoChiTiet: [],
    dsThongSoMay: [],
    dsDichVuThuocId: [],
    dsDichVuKyThuatId: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { maBaoCao } = useParams();
  const location = useLocation().pathname;
  const headless = location.indexOf("headless-editor") != -1;
  const { values } = useSelector((state) => state.vitalSigns);

  const { editorId } = useContext(EMR2Context);
  const chanKyBaoCao = useEditor(editorId, "chanKyBaoCao", "");
  const listBacSi = useStore("nhanVien.listBacSi", []);

  const { chiDinhTuLoaiDichVu, ngoaiTru = false } = getAllQueryString();

  const chiSoChiTiet = (_toDieuTri, isToDieuTriSanKhoa = false) => {
    const thoiGianYLenh = moment(_toDieuTri.thoiGianYLenh).format("DD/MM/YYYY");

    if (isToDieuTriSanKhoa) {
      let _dsChiSoSong = (values || []).filter(
        (item) =>
          !!item.thoiGianThucHien &&
          item.chiDinhTuLoaiDichVu == LOAI_DICH_VU.NOI_TRU
      );
      if (_dsChiSoSong.length == 0) {
        _dsChiSoSong = (values || []).filter(
          (item) =>
            !!item.thoiGianThucHien &&
            [LOAI_DICH_VU.KHAM, LOAI_DICH_VU.TIEP_DON].includes(
              item.chiDinhTuLoaiDichVu
            )
        );
      }

      const _selectedCSS = findClosestChiSoSong(
        _toDieuTri.thoiGianYLenh,
        _dsChiSoSong
      );

      if (_selectedCSS.id) {
        return {
          ..._selectedCSS,
          huyetAp: `${_selectedCSS.huyetApTamThu || ""}${
            _selectedCSS.huyetApTamTruong
              ? `/${_selectedCSS.huyetApTamTruong}`
              : ""
          }`,
        };
      }
    } else if (ngoaiTru) {
      let _dsChiSoSong = (values || []).filter(
        (item) =>
          !!item.thoiGianThucHien &&
          item.chiDinhTuLoaiDichVu == LOAI_DICH_VU.KHAM &&
          item.chiDinhTuDichVuId == _toDieuTri.id
      );
      if (_dsChiSoSong.length == 0) {
        _dsChiSoSong = (values || []).filter(
          (item) =>
            !!item.thoiGianThucHien &&
            [LOAI_DICH_VU.KHAM, LOAI_DICH_VU.TIEP_DON].includes(
              item.chiDinhTuLoaiDichVu
            )
        );
      }

      const _selectedCSS = findClosestChiSoSong(
        _toDieuTri.thoiGianThucHien,
        _dsChiSoSong
      );
      if (_selectedCSS.id) {
        return {
          ..._selectedCSS,
          huyetAp: `${_selectedCSS.huyetApTamThu || ""}${
            _selectedCSS.huyetApTamTruong
              ? `/${_selectedCSS.huyetApTamTruong}`
              : ""
          }`,
        };
      }
    } else {
      if (dsChiSoSong[thoiGianYLenh]) {
        const data = dsChiSoSong[thoiGianYLenh]
          .filter(
            (item) =>
              new Date(item.thoiGianThucHien).getTime() <=
              new Date(_toDieuTri.thoiGianYLenh).getTime()
          )
          .sort(
            (a, b) =>
              new Date(b.thoiGianThucHien).getTime() -
              new Date(a.thoiGianThucHien).getTime()
          )[0];
        return data;
      } else {
        return {};
      }
    }
  };

  const {
    cheDoChamSoc: { getListAllCheDoChamSoc },
    application: { onGetThongTinBenhVien },
  } = useDispatch();

  const [listDonViTocDoTruyen] = useEnum(ENUM.DON_VI_TOC_DO_TRUYEN);
  const [listNhomMau] = useEnum(ENUM.NHOM_MAU);
  const [listTheBenhLao] = useEnum(ENUM.THE_BENH_LAO);
  const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);
  const [listTrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);
  const logoAnh = useSelector((state) => state.application.logo);
  const [TO_DIEU_TRI_IN_THONG_TIN_HANH_CHINH] = useThietLap(
    "TO_DIEU_TRI_IN_THONG_TIN_HANH_CHINH"
  );
  const [dataLOAI_BA_SAN_KHOA] = useThietLap(
    THIET_LAP_CHUNG.LOAI_BA_SAN_KHOA,
    ""
  );
  const auth = useSelector((state) => state.auth.auth);
  const [listAllKhoa] = useListAll("khoa", [], true);
  const [listAllLoaiBenhAn] = useListAll("loaiBenhAn", {}, true);

  const listAllCheDoChamSoc = useStore("cheDoChamSoc.listAllCheDoChamSoc", []);
  const listChamSoc = useMemo(() => {
    return listAllCheDoChamSoc.map((item) => ({
      label: item.ten,
      value: item.id,
    }));
  }, [listAllCheDoChamSoc]);

  useEffect(() => {
    getListAllCheDoChamSoc({ page: "", size: "", active: true });
    moment.locale("vi", {
      weekdays: "Chủ nhật_Thứ 2_Thứ 3_Thứ 4_Thứ 5_Thứ 6_Thứ 7".split("_"),
    });
  }, []);

  useEffect(() => {
    if (auth) {
      onGetThongTinBenhVien({ benhVienId: auth?.benhVien?.id });
    }
  }, [auth]);

  const dataNhomMau = useMemo(() => {
    return listNhomMau.map((item) => ({
      value: item.id,
      label: item.ten,
    }));
  }, [listNhomMau]);

  const isTDTSoSinhMemo = useMemo(() => {
    const _ngayDieuTri = (toDieuTri.ngayDieuTris || [])?.[0];
    if (_ngayDieuTri) {
      const _dsTinhChatKhoa =
        listAllKhoa.find((x) => x.id === _ngayDieuTri?.khoaChiDinhId)
          ?.dsTinhChatKhoa || [];

      return (
        _dsTinhChatKhoa.includes(DS_TINH_CHAT_KHOA.TO_DIEU_TRI_SO_SINH) &&
        (form?.tuoi || 0) < 6
      );
    }

    return false;
  }, [toDieuTri?.ngayDieuTris, listAllKhoa, form?.tuoi]);

  const checkChuyenKhoaToDieuTri = (id, khoaChiDinhId) => {
    const _dsTinhChatKhoa =
      listAllKhoa.find((x) => x.id === khoaChiDinhId)?.dsTinhChatKhoa || [];

    const _listAllKhoaSanIds = listAllKhoa
      .filter((x) =>
        (x.dsTinhChatKhoa || []).includes(
          DS_TINH_CHAT_KHOA.TO_DIEU_TRI_SAN_KHOA
        )
      )
      .map((x) => x.id);

    //SAKURA-74997: Sửa lại lấy tờ điều trị đầu tiên của tất cả các khoa sản
    const _toDieuTriIdDauTienCuaKhoaSan = orderBy(
      (listToDieuTriByNb || []).filter((x) =>
        _listAllKhoaSanIds.includes(x.khoaChiDinhId)
      ),
      "id",
      "asc"
    )?.[0]?.id;
    //Lấy mã loại bệnh án của NB
    const _maLoaiBANb = listAllLoaiBenhAn.find(
      (x) => x.id == form.loaiBenhAnId
    );
    const listLoaiBASanKhoa = dataLOAI_BA_SAN_KHOA?.split(",") || [];

    let isToDieuTriSanKhoa =
      listLoaiBASanKhoa.includes(_maLoaiBANb?.ma) &&
      id == _toDieuTriIdDauTienCuaKhoaSan &&
      _dsTinhChatKhoa.includes(DS_TINH_CHAT_KHOA.TO_DIEU_TRI_SAN_KHOA) &&
      (form?.tuoi || 0) >= 6;

    let isToDieuTriSoSinh =
      _dsTinhChatKhoa.includes(DS_TINH_CHAT_KHOA.TO_DIEU_TRI_SO_SINH) &&
      (form?.tuoi || 0) < 6;

    return {
      isToDieuTriSanKhoa,
      isToDieuTriSoSinh,
    };
  };

  const onChange =
    (key, ngayDieuTri, index, toDieuTriId, isEditDienBien) => (e) => {
      if (
        key === "cheDoChamSocId" ||
        key === "theBenhLao" ||
        key === "dsCdChinhId" ||
        key === "dsCdKemTheoId" ||
        key === "dsDichVuThuocId" ||
        key === "dsDichVuKyThuatId"
      ) {
        onFormChange(
          key,
          ngayDieuTri?.idGuid,
          index,
          toDieuTriId,
          isEditDienBien
        )(e);
      } else if (key === "boSung") {
        let value = (e || "").replaceAll("<br>", "\n");
        value = stripHtml(value);
        onFormChange(
          key,
          ngayDieuTri?.idGuid,
          index,
          toDieuTriId,
          isEditDienBien
        )(value);
        ngayDieuTri[key] = value;
      } else if (key === "chuKyBacSiDieuTri") {
        onFormChange(
          key,
          ngayDieuTri?.idGuid,
          index,
          toDieuTriId,
          isEditDienBien
        )(e);
      } else {
        let value = (e || "").replaceAll("<br>", "\n");
        onFormChange(
          key,
          ngayDieuTri?.idGuid,
          index,
          toDieuTriId,
          isEditDienBien
        )(value);
      }
      if (
        key === "dsDichVuThuocId" ||
        key === "dsDichVuKyThuatId" ||
        (key === "thoiGianThucHien" && maBaoCao === "EMR_BA077.2")
      ) {
        setState({ [key]: { ...state[key], [index]: e } });
      }
    };

  const getValueHuyetAp = (value) => {
    if (!value) {
      return {
        huyetApTamThu: 0,
        huyetApTamTruong: 0,
      };
    }
    if ((value || "").split("/").length - 1 !== 1) {
      message.error(t("editor.nhapSaiQuyTac.NhapDungViDu:120/90"));
      return;
    }
    const arr = (value || "").split("/");
    if (arr.length === 2) {
      const huyetApTamThu = +arr[0];
      const huyetApTamTruong = +arr[1];
      if (huyetApTamThu < huyetApTamTruong) {
        message.error(t("editor.huyetApTamThuCanLonHonHuyetApTamTruong"));
      } else {
        return {
          huyetApTamThu,
          huyetApTamTruong,
        };
      }
    }
  };

  const onChangeChiSoSong =
    (chiSoSong, fieldName, ngayDieuTri, index, toDieuTriId, isEditDienBien) =>
    (value) => {
      if (!chiSoSong?.id) {
        chiSoSong.thoiGianThucHien = ngayDieuTri.thoiGianYLenh;
        chiSoSong.nguoiThucHienId = ngayDieuTri.bacSiDieuTriId;
        chiSoSong.tenNguoiThucHien = ngayDieuTri.tenBacSiDieuTri;
        chiSoSong.nbDotDieuTriId = ngayDieuTri.nbDotDieuTriId;
        if (fieldName === "huyetAp") {
          const { huyetApTamThu, huyetApTamTruong } = getValueHuyetAp(value);
          chiSoSong.huyetApTamThu = huyetApTamThu;
          chiSoSong.huyetApTamTruong = huyetApTamTruong;
        }
        chiSoSong[fieldName] = value;
      } else {
        if (fieldName === "huyetAp") {
          const { huyetApTamThu, huyetApTamTruong } = getValueHuyetAp(value);
          chiSoSong.huyetApTamThu = huyetApTamThu;
          chiSoSong.huyetApTamTruong = huyetApTamTruong;
        }
        chiSoSong[fieldName] = value;
      }
      onFormChange(
        "chiSoSong",
        ngayDieuTri?.idGuid,
        index,
        toDieuTriId,
        isEditDienBien
      )(chiSoSong);
    };

  const onChangeChiSoSongKhac =
    (chiSoSong, index2, chiSo, ngayDieuTri, toDieuTriId, isEditDienBien) =>
    (value) => {
      const index = chiSoSong.dsChiSoSongKhac.findIndex(
        (item) => item.chiSoSongId == chiSo.id
      );
      if (value) {
        if (index > -1) {
          chiSoSong.dsChiSoSongKhac[index].giaTri = value;
        } else {
          chiSoSong.dsChiSoSongKhac = chiSoSong.dsChiSoSongKhac || [];
          chiSoSong.dsChiSoSongKhac.push({
            chiSoSongId: chiSo.id,
            giaTri: value,
          });
        }
      } else {
        if (index != -1) chiSoSong.dsChiSoSongKhac.splice(index, 1);
      }

      onFormChange(
        "chiSoSong",
        ngayDieuTri?.idGuid,
        null,
        toDieuTriId,
        isEditDienBien
      )(chiSoSong);
    };

  const dienBienBenhNgoaiTru = (
    ngayDieuTri,
    index,
    isReadOnly,
    toDieuTriId,
    isEditDienBien
  ) => {
    //nếu có thiết lập khóa phiếu sau ký => disabled những tờ điều trị đã được ký
    const _isDisable =
      isDisable || (khoaPhieuSauKhiKy && !!ngayDieuTri?.lichSuKy?.id);

    return listSapXepDienBienBenh
      .filter((item) => item.show)
      .map((item) => {
        return renderDienBienNgoaiTru({
          key: item.fieldName,
          ngayDieuTri,
          index,
          onChange,
          listTheBenhLao,
          state,
          isDisable: _isDisable,
          isReadOnly,
          toDieuTriId,
          isEditDienBien,
        });
      });
  };

  const dienBienBenhSanKhoa = (ngayDieuTri, index, isReadOnly) => {
    //nếu có thiết lập khóa phiếu sau ký => disabled những tờ điều trị đã được ký
    const _isDisable =
      isDisable || (khoaPhieuSauKhiKy && !!ngayDieuTri?.lichSuKy?.id);
    const chiSoSong = chiSoChiTiet(ngayDieuTri, true);

    const localProps = {
      form,
      mode,
      ngayDieuTri,
      index,
      onFormChange,
      isDisable: _isDisable,
      isReadOnly,
      chiSoSong,
      onChangeChiSoSong,
      renderChanDoan: ["dsCdChinhId", "dsCdKemTheoId", "moTa"].map((item) => {
        return renderDienBienNgoaiTru({
          key: item,
          ngayDieuTri,
          index,
          onChange,
          state,
          isDisable: _isDisable,
          isReadOnly,
        });
      }),
    };

    return <DienBienSanKhoa {...localProps} />;
  };

  const dienBienBenhSoSinh = (ngayDieuTri, index, isReadOnly) => {
    //nếu có thiết lập khóa phiếu sau ký => disabled những tờ điều trị đã được ký
    const _isDisable =
      isDisable || (khoaPhieuSauKhiKy && !!ngayDieuTri?.lichSuKy?.id);

    const localProps = {
      form,
      mode,
      ngayDieuTri,
      index,
      onFormChange,
      isDisable: _isDisable,
      isReadOnly,
    };

    return <MainDienBienSoSinh {...localProps} />;
  };

  const dienBienBenh = (
    ngayDieuTri,
    index,
    isReadOnly,
    toDieuTriId,
    isEditDienBien
  ) => {
    let chiSoSong = isDisable
      ? ngayDieuTri.chiSoSong
      : chiSoChiTiet(ngayDieuTri);
    //nếu có thiết lập khóa phiếu sau ký => disabled những tờ điều trị đã được ký
    const _isDisable =
      isDisable || (khoaPhieuSauKhiKy && !!ngayDieuTri?.lichSuKy?.id);
    if (ngoaiTru && index > 0) {
      chiSoSong = ngayDieuTri.chiSoSong;
    }
    if (ngayDieuTri) {
      const _showCSChiTiet =
        state.showChiSoChiTiet && state.showChiSoChiTiet[index];

      //kiểm tra tờ điều trị đầu tiên có khoa chỉ định có tính chất TỜ ĐIỀU TRỊ SẢN KHOA (160) || TỜ ĐIỀU TRỊ SƠ SINH (170)
      const { isToDieuTriSanKhoa, isToDieuTriSoSinh } =
        checkChuyenKhoaToDieuTri(ngayDieuTri.id, ngayDieuTri.khoaChiDinhId);
      return (
        <>
          {_showCSChiTiet &&
            maBaoCao !== "EMR_BA077.2" &&
            renderThongSoChung({
              chiSoSong: chiSoSong || {},
              onChangeChiSoSong,
              onChangeChiSoSongKhac,
              isDisable: _isDisable,
              ngayDieuTri,
              listCss,
              dataNhomMau,
              index,
              toDieuTriId,
              isEditDienBien,
            })}

          <div className="ml5">
            {isToDieuTriSanKhoa ? (
              dienBienBenhSanKhoa(ngayDieuTri, index, isReadOnly)
            ) : isToDieuTriSoSinh ? (
              dienBienBenhSoSinh(ngayDieuTri, index, isReadOnly)
            ) : ngoaiTru ? (
              dienBienBenhNgoaiTru(
                ngayDieuTri,
                index,
                isReadOnly,
                toDieuTriId,
                isEditDienBien
              )
            ) : (
              <div className="dien-bien ">
                <div className="b">{t("editor.toDieuTri.dienBienBenh")}:</div>
                <DeboundInput
                  rows={1}
                  value={formatText(ngayDieuTri.dienBienBenh)}
                  onChange={onChange(
                    "dienBienBenh",
                    ngayDieuTri,
                    index,
                    toDieuTriId,
                    isEditDienBien
                  )}
                  type="multipleline"
                  lineHeightText={1.5}
                  fontSize={12}
                  minHeight={12 + 6}
                  readOnly={_isDisable}
                  disabled={_isDisable}
                  markSpanRow={itemProps.markSpanRow}
                />
              </div>
            )}

            {itemProps.viTriDichVu === 1 &&
              renderListDv(ngayDieuTri, {
                hienThiDvktNgungYLenhKhongThucHien,
                hienThiTextTrangThaiHoanNgungYLenhKhongThucHienDvkt,
                listTrangThaiHoan,
              })}
            {itemProps.viTriDichVu === 1 &&
              renderListDvNgoaiTru(ngayDieuTri?.dsDvKt || [], {
                hienThiDvktNgungYLenhKhongThucHien,
                hienThiTextTrangThaiHoanNgungYLenhKhongThucHienDvkt,
                listTrangThaiHoan,
              })}
            {itemProps.viTriChanKyBacSi === 1 && (
              <span
                style={{
                  alignSelf: "center",
                  marginTop: 30,
                  paddingBottom: 30,
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                }}
                className="sign"
              >
                <ImageSign
                  component={{
                    props: {
                      fieldName: "chuKy1",
                      fontSize: itemProps.fontSizeCa || 12,
                      alignCa: "center",
                      contentColor: itemProps.contentColor || "red",
                      width: 150,
                      height: 100,
                      viTriCa: itemProps.viTriCa,
                      showCa: itemProps.showCa,
                      viTriAnhCa: itemProps.viTriAnhCa,
                      widthCa: itemProps.widthCa,
                      allowReset:
                        itemProps.allowReset == undefined
                          ? true
                          : itemProps.allowReset,
                      capKy: itemProps.capKy || 1,
                      loaiKy: itemProps.loaiKy,
                      showPatientSign: itemProps.showPatientSign,
                      disabled: mode === MODE.config,
                      isCheckKyBacSi: isCheckKyBacSi,
                      dataSign: {
                        kySo: ngayDieuTri.kySo,
                        soPhieu: ngayDieuTri.soPhieu || ngayDieuTri.id,
                        lichSuKyId: ngayDieuTri.lichSuKyId,
                        tenChanKy: ngayDieuTri.dsTenChanKy1?.length
                          ? ngayDieuTri.dsTenChanKy1[0]
                          : "",
                        chuKySo: 2,
                        baoCaoId: maBaoCao,
                        nbDotDieuTriId: ngayDieuTri.nbDotDieuTriId,
                        trangThaiKy: ngayDieuTri.trangThaiChanKy1,
                        id: ngayDieuTri.id,
                        maBaoCao: maBaoCao,
                      },
                    },
                  }}
                  mode={mode}
                  form={ngayDieuTri}
                ></ImageSign>

                {hienBSDieuTri && (
                  <b>
                    {ngayDieuTri?.vietTatHhhvBsDieuTri || ""}{" "}
                    {ngayDieuTri?.tenBacSiDieuTri || ""}
                  </b>
                )}
              </span>
            )}
            {itemProps.gopToDieuTriNhieuNgay && (
              <>
                <Col span={24}>
                  <b>{"Chẩn đoán: "}</b>
                  <span>{`${ngayDieuTri.moTaToDieuTri || ""}`}</span>
                </Col>
              </>
            )}
            {itemProps.hienThiCdPhanBietDienBien && (
              <Col span={24}>
                <b>{"Chẩn đoán phân biệt: "}</b>
                <span>{`${ngayDieuTri.cdPhanBietToDieuTri || ""}`}</span>
              </Col>
            )}
          </div>
        </>
      );
    } else {
      return <></>;
    }
  };

  const handleClick = (key, ngayDieuTri, index) => () => {
    let _stateKey = state[key];
    _stateKey[index] = !_stateKey[index];
    setState({
      [key]: _stateKey,
    });
    onFormChange([key], ngayDieuTri?.idGuid)(_stateKey[index]);
  };

  const content = (ngayDieuTri, index) => () => {
    return (
      <div className="popover-y-lenh">
        <div
          onClick={handleClick("showBoSung", ngayDieuTri, index)}
          className={state.showBoSung[index] ? "active" : ""}
        >
          {t("editor.themThongTinBoSung")}{" "}
          {state.showBoSung[index] && <CheckOutlined />}
        </div>

        {!ngoaiTru && (
          <div
            onClick={handleClick("showBangThongSo", ngayDieuTri, index)}
            className={state.showBangThongSo[index] ? "active" : ""}
          >
            {t("editor.themBangThongSo")}{" "}
            {state.showBangThongSo[index] && <CheckOutlined />}
          </div>
        )}

        <div
          onClick={handleClick("showChiSoChiTiet", ngayDieuTri, index)}
          className={state.showChiSoChiTiet[index] ? "active" : ""}
        >
          {t("editor.themChiSoChiTiet")}{" "}
          {state.showChiSoChiTiet[index] && <CheckOutlined />}
        </div>
      </div>
    );
  };

  const onChangeTenMau =
    (data, index, bang, ngayDieuTri, indexToDieuTri) => () => {
      const payload = [
        ...data.dsThongSo.map((item) => ({ ...item, bang })),
        ...state.dsThongSoMay[indexToDieuTri].filter((x) => x.bang !== bang),
      ];

      let dsThongSoMay = state.dsThongSoMay;
      dsThongSoMay[indexToDieuTri] = payload || [];

      setState({
        dsThongSoMay,
        index: index,
      });
      onFormChange(
        "dsThongSoMay",
        ngayDieuTri?.idGuid
      )(dsThongSoMay[indexToDieuTri]);
    };

  const contentMau = (bang, ngayDieuTri, indexToDieuTri) => {
    return (
      <div className="popover-y-lenh">
        {(listThongSo || []).map((item, index) => {
          return (
            <div
              onClick={onChangeTenMau(
                item,
                index,
                bang,
                ngayDieuTri,
                indexToDieuTri
              )}
              className={index === state.index ? "active" : ""}
            >
              {item.ten} {index === state.index && <CheckOutlined />}
            </div>
          );
        })}
      </div>
    );
  };

  const onChangeThongSo =
    (key, index, data, ngayDieuTri, indexToDieuTri) => (e) => {
      let value = "";
      if (e?.target) {
        value = e.target.value;
      } else {
        value = e;
      }

      data[index][key] = value;
      const dsThongSoMay = [
        ...state.dsThongSoMay[indexToDieuTri].filter(
          (x) => data[0]?.bang !== x.bang
        ),
        ...data,
      ];
      onFormChange("dsThongSoMay", ngayDieuTri?.idGuid, index)(dsThongSoMay);
    };

  const onMergeColumns = (index, data, ngayDieuTri, indexToDieuTri) => () => {
    const item = data[index];
    //hienThi 1 merge cột giá trị vào mã, loại 2 merge cột mã vào giá trị
    if (item.ma && item.giaTri) {
      item.hienThi = 1;
      item.giaTri = null;
    } else if (item.ma && !item.giaTri) {
      item.hienThi = 1;
    } else if (!item.ma && item.giaTri) {
      item.hienThi = 2;
    }
    item.soCot = 2;

    let dsThongSoMay = state.dsThongSoMay;

    const _dsThongSoMay = [
      ...state.dsThongSoMay[indexToDieuTri].filter(
        (x) => data[0]?.bang !== x.bang
      ),
      ...data,
    ];
    dsThongSoMay[indexToDieuTri] = _dsThongSoMay;

    setState({ dsThongSoMay });
    onFormChange(
      "dsThongSoMay",
      ngayDieuTri?.idGuid
    )(dsThongSoMay[indexToDieuTri]);
  };

  const onTachCot = (index, data, ngayDieuTri, indexToDieuTri) => () => {
    const item = data[index];
    //hienThi 1 merge cột giá trị vào mã, loại 2 merge cột mã vào giá trị
    if (!item?.hienThi) {
      return;
    }
    item.ma = item.ma || item.giaTri;
    item.hienThi = null;
    item.giaTri = null;

    let dsThongSoMay = state.dsThongSoMay;
    const _dsThongSoMay = [
      ...state.dsThongSoMay[indexToDieuTri].filter(
        (x) => data[0]?.bang !== x.bang
      ),
      ...data,
    ];
    dsThongSoMay[indexToDieuTri] = _dsThongSoMay;

    setState({ dsThongSoMay });
    onFormChange(
      "dsThongSoMay",
      ngayDieuTri?.idGuid
    )(dsThongSoMay[indexToDieuTri]);
  };

  const onAddThongSo = (index, ngayDieuTri, indexToDieuTri) => () => {
    let dsThongSoMay = state.dsThongSoMay;

    const _dsThongSoMay = [
      ...state.dsThongSoMay[indexToDieuTri],
      {
        ma: "",
        giaTri: "",
        bang: index || 0,
      },
    ];

    dsThongSoMay[indexToDieuTri] = _dsThongSoMay;
    setState({
      dsThongSoMay,
    });
    onFormChange(
      "dsThongSoMay",
      ngayDieuTri?.idGuid
    )(dsThongSoMay[indexToDieuTri]);
  };

  const onRemoveThongSo = useCallback(
    (index, data, ngayDieuTri, indexToDieuTri) => () => {
      let dsThongSoMay = state.dsThongSoMay;

      const _dsThongSoMay = [
        ...data.filter((x, idx) => idx !== index),
        ...state.dsThongSoMay[indexToDieuTri].filter(
          (x) => data[0]?.bang !== x.bang
        ),
      ];

      dsThongSoMay[indexToDieuTri] = _dsThongSoMay;
      setState({
        dsThongSoMay: dsThongSoMay,
      });
      onFormChange(
        "dsThongSoMay",
        ngayDieuTri?.idGuid
      )(dsThongSoMay[indexToDieuTri]);
    },
    [state.dsThongSoMay]
  );

  const onAddTableThongSoMay = (listThongSoMay, ngayDieuTri, index) => () => {
    let data = listThongSoMay.reduce((max, shot) => {
      return shot.bang >= (max.bang || 0) ? shot : max;
    }, {});
    const _dsThongSoMay = [
      ...state.dsThongSoMay[index],
      {
        ma: "",
        giaTri: "",
        bang: (data?.bang || 0) + 1,
      },
    ];
    let dsThongSoMay = state.dsThongSoMay;
    dsThongSoMay[index] = _dsThongSoMay;

    setState({
      dsThongSoMay,
    });
    onFormChange("dsThongSoMay", ngayDieuTri?.idGuid)(dsThongSoMay[index]);
  };

  const onDeleteTableThongSoMay = (bang, ngayDieuTri, indexToDieuTri) => () => {
    showConfirm(
      {
        title: "",
        content: t("editor.xacNhanXoaBangThongSo"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        showBtnOk: true,
      },
      () => {
        let dsThongSoMay = state.dsThongSoMay;

        const _dsThongSoMay = state.dsThongSoMay[indexToDieuTri].filter(
          (x) => x.bang !== bang
        );

        dsThongSoMay[indexToDieuTri] = _dsThongSoMay;
        setState({
          dsThongSoMay,
        });
        onFormChange(
          "dsThongSoMay",
          ngayDieuTri?.idGuid
        )(dsThongSoMay[indexToDieuTri]);
      }
    );
  };

  const reorder = (list, startIndex, endIndex) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);
    return result;
  };

  const onDragEnd = (dataSource, ngayDieuTri) => (result) => {
    if (!result.destination) {
      return;
    }
    const dsThongSoMay = reorder(
      Object.entries(dataSource),
      result.source.index,
      result.destination.index
    );
    let dataThongSoMay = [];
    dsThongSoMay.forEach((item, index) => {
      const [key, record] = item;
      record.forEach((x) => {
        dataThongSoMay = [
          ...dataThongSoMay,
          {
            ...x,
            bang: index + 1,
          },
        ];
      });
    });
    setState({
      dsThongSoMay: dataThongSoMay,
    });
    onFormChange("dsThongSoMay", ngayDieuTri?.idGuid)(dataThongSoMay);
  };

  const contentConfig = (index, data, ngayDieuTri, indexToDieuTri) => (
    <Menu
      items={[
        {
          key: 0,
          label: (
            <a
              href={() => false}
              onClick={() => {
                onMergeColumns(index, data, ngayDieuTri, indexToDieuTri)();
              }}
            >
              {t("editor.toDieuTri.gopCot")}
            </a>
          ),
        },
        {
          key: 0,
          label: (
            <a
              href={() => false}
              onClick={() => {
                onTachCot(index, data, ngayDieuTri, indexToDieuTri)();
              }}
            >
              {t("editor.toDieuTri.tachCot")}
            </a>
          ),
        },
      ]}
    />
  );

  const renderHeaderThongSo = (
    item,
    index,
    data,
    ngayDieuTri,
    indexToDieuTri
  ) => (
    <thead>
      <tr>
        <td
          className="b center"
          colSpan={item.hienThi ? 2 : 1}
          {...(item.hienThi === 2
            ? {
                style: {
                  display: "none",
                },
              }
            : {})}
        >
          <Dropdown
            placement="bottom"
            overlay={contentConfig(index, data, ngayDieuTri, indexToDieuTri)}
            size="small"
            trigger={["click"]}
          >
            <Button
              size="small"
              className="icon-config"
              icon={<SettingOutlined />}
            ></Button>
          </Dropdown>
          <div style={{ display: "flex" }}>
            <div style={{ flex: "1" }}>
              <TextField
                onChange={onChangeThongSo(
                  "ma",
                  index,
                  data,
                  ngayDieuTri,
                  indexToDieuTri
                )}
                html={parseTextToHtml(item?.ma)}
                style={{
                  fontWeight: "bold",
                  textAlign: "center",
                }}
              />
            </div>
            <Popover
              content={contentMau(data[0]?.bang, ngayDieuTri, indexToDieuTri)}
            >
              <DownOutlined
                className="icon"
                style={{
                  float: "right",
                  marginTop: "3px",
                }}
              />
            </Popover>
          </div>
        </td>
        <td
          className="b center"
          colSpan={item.hienThi ? 2 : 1}
          {...(item.hienThi === 1
            ? {
                style: {
                  display: "none",
                },
              }
            : {})}
        >
          <TextField
            onChange={onChangeThongSo(
              "giaTri",
              index,
              data,
              ngayDieuTri,
              indexToDieuTri
            )}
            html={parseTextToHtml(item?.giaTri)}
            style={{
              fontWeight: "bold",
              textAlign: "center",
            }}
          />
        </td>
      </tr>
    </thead>
  );

  const renderDsThongSoMay = (dsThongSoMay, indexToDieuTri, ngayDieuTri) => {
    if (state.showBangThongSo && state.showBangThongSo[indexToDieuTri]) {
      if ((isDisable && dsThongSoMay?.length) || !isDisable) {
        const dataSource = groupBy(dsThongSoMay, "bang");

        return (
          <div className="table-may">
            <div
              style={{ display: "flex", flexDirection: "column", flex: "1" }}
            >
              <DragDropContext onDragEnd={onDragEnd(dataSource, ngayDieuTri)}>
                <Droppable droppableId="droppable">
                  {(provided, snapshot) => (
                    <div {...provided.droppableProps} ref={provided.innerRef}>
                      {Object.keys(dataSource)?.map((key, index) => (
                        <Draggable draggableId={key} index={index}>
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                            >
                              <div style={{ flex: "1" }}>
                                <div style={{ display: "flex" }}>
                                  <table className="table-thong-so">
                                    {(dataSource[key] || []).map(
                                      (item, index) => {
                                        if (index === 0) {
                                          return renderHeaderThongSo(
                                            item,
                                            index,
                                            dataSource[key],
                                            ngayDieuTri,
                                            indexToDieuTri
                                          );
                                        }
                                        return (
                                          <tbody>
                                            <RenderBangThongSo
                                              key={index}
                                              item={item}
                                              isDisable={isDisable}
                                              onChangeThongSo={onChangeThongSo}
                                              index={index}
                                              onRemoveThongSo={onRemoveThongSo}
                                              data={dataSource[key]}
                                              onMergeColumns={onMergeColumns}
                                              onTachCot={onTachCot}
                                              ngayDieuTri={ngayDieuTri}
                                              indexToDieuTri={indexToDieuTri}
                                            ></RenderBangThongSo>
                                          </tbody>
                                        );
                                      }
                                    )}
                                  </table>
                                  <Button
                                    icon={<SVG.IcDelete />}
                                    size="small"
                                    className="btn-add-thong-so"
                                    onClick={onDeleteTableThongSoMay(
                                      dataSource[key][0]?.bang,
                                      ngayDieuTri,
                                      indexToDieuTri
                                    )}
                                  ></Button>
                                </div>

                                {!isDisable && (
                                  <Button
                                    icon={<PlusOutlined />}
                                    size="small"
                                    className="btn-add-thong-so"
                                    onClick={onAddThongSo(
                                      dataSource[key][0]?.bang,
                                      ngayDieuTri,
                                      indexToDieuTri
                                    )}
                                  ></Button>
                                )}
                              </div>
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </DragDropContext>
            </div>

            <div>
              <Button
                icon={<SVG.IcAdd />}
                size="small"
                className="btn-add-thong-so"
                onClick={onAddTableThongSoMay(
                  dsThongSoMay,
                  ngayDieuTri,
                  indexToDieuTri
                )}
              ></Button>
            </div>
          </div>
        );
      }
    }
  };

  useEffect(() => {
    if (toDieuTri.ngayDieuTris && toDieuTri.ngayDieuTris.length === 1) {
      let _showChiSoChiTiet = [toDieuTri.ngayDieuTris[0]?.thietLap?.chiSoSong];
      if (ngoaiTru) {
        if (toDieuTri.ngayDieuTris[0]?.thietLap?.chiSoSong === false) {
          _showChiSoChiTiet = [false];
        } else {
          _showChiSoChiTiet = [true];
        }
      }

      setState({
        dsThongSoMay: [
          toDieuTri.ngayDieuTris[0].dsThongSoMay?.map((item, index) => ({
            ...item,
            bang: item.bang || 0,
          })) || [],
        ],
        showBangThongSo: [toDieuTri.ngayDieuTris[0]?.thietLap?.thongSo],
        showBoSung: ngoaiTru
          ? [!!toDieuTri.ngayDieuTris[0]?.thietLap?.boSung]
          : [!!toDieuTri.ngayDieuTris[0].boSung],
        boSung: toDieuTri.ngayDieuTris[0].boSung,
        showChiSoChiTiet: _showChiSoChiTiet,
      });
    } else if (toDieuTri.ngayDieuTris.length > 1) {
      setState({
        dsThongSoMay: (toDieuTri.ngayDieuTris || []).map(
          (item) =>
            item.dsThongSoMay?.map((item, index) => ({
              ...item,
              bang: item.bang || 0,
            })) || []
        ),
        showBoSung: (toDieuTri.ngayDieuTris || []).map((item) =>
          ngoaiTru ? !!item?.thietLap?.boSung : !!item?.boSung
        ),
        showBangThongSo: (toDieuTri.ngayDieuTris || []).map(
          (item) => !!item?.thietLap?.thongSo
        ),
        showChiSoChiTiet: (toDieuTri.ngayDieuTris || []).map((item, index) => {
          let _showChiSoChiTiet = !!item?.thietLap?.chiSoSong;
          if (ngoaiTru) {
            if (item?.thietLap?.chiSoSong === false) {
              _showChiSoChiTiet = false;
            } else {
              _showChiSoChiTiet = true;
            }
          }
          return _showChiSoChiTiet;
        }),
      });
    }
  }, [toDieuTri.ngayDieuTris, itemProps, maBaoCao]);

  const listThongSo = useMemo(() => {
    return (itemProps.defaultThongSo || []).map((item, index) => {
      return {
        id: index,
        ten: item.tenMau,
        dsThongSo: item.dsThongSo,
      };
    });
  }, [itemProps.defaultThongSo]);

  const renderContent = ({
    key,
    ngayDieuTri,
    index,
    dsPhanLoaiPHCN,
    isDisableNgoaiTru,
    toDieuTriId,
    isEditDienBien,
    isShowSoLuongThuoc = false,
  }) => {
    //nếu có thiết lập khóa phiếu sau ký => disabled những tờ điều trị đã được ký
    const _isDisable =
      isDisable || (khoaPhieuSauKhiKy && !!ngayDieuTri?.lichSuKy?.id);

    switch (key) {
      case "boSung":
        return (
          (_isDisable
            ? !!ngayDieuTri.boSung
            : state.showBoSung && state.showBoSung[index]) && (
            <DeboundInput
              rows={1}
              readOnly={_isDisable}
              value={formatText(ngayDieuTri.boSung)}
              onChange={onChange(
                "boSung",
                ngayDieuTri,
                index,
                toDieuTriId,
                isEditDienBien
              )}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={12}
              minHeight={12 + 6}
              disabled={_isDisable}
              // focusLastCharacter={true}
              markSpanRow={itemProps.markSpanRow}
            ></DeboundInput>
          )
        );
      case "dsThongSoMay":
        return renderDsThongSoMay(
          _isDisable
            ? ngayDieuTri.dsThongSoMay || []
            : state.dsThongSoMay
            ? state.dsThongSoMay[index]
            : [],
          index,
          ngayDieuTri
        );
      case "thuoc":
        return (
          <YLenh
            data={ngayDieuTri}
            listDonViTocDoTruyen={listDonViTocDoTruyen}
            disable={_isDisable}
            onFormChange={onFormChange}
            showDuongDungThuoc={showDuongDungThuoc}
            hienSoLuongDvt={hienSoLuongDvt}
            hienThuocNhaThuoc={hienThuocNhaThuoc}
            hienThuocKeNgoai={hienThuocKeNgoai}
            hienThuocTuVan={hienThuocTuVan}
            listLoaiChiDinh={listLoaiChiDinh}
            listTrangThaiHoan={listTrangThaiHoan}
            hienLoaiChiDinh={hienLoaiChiDinh}
            hienThiSLBuoi={hienThiSLBuoi}
            hienThiTocDoTruyen={hienThiTocDoTruyen}
            hienThiDonViTocDoTruyen={hienThiDonViTocDoTruyen}
            hienThiDvKhoNgungYLenh={hienThiDvKhoNgungYLenh}
            hienThiTextTrangThaiHoanNgungYLenhDvKho={
              hienThiTextTrangThaiHoanNgungYLenhDvKho
            }
            hienThiSoLuongYeuCauDvKho={hienThiSoLuongYeuCauDvKho}
            maBaoCao={maBaoCao}
            itemProps={itemProps}
          />
        );
      case "ghiChu":
        const key = ngoaiTru ? "ghiChuNoiTru" : "ghiChu";

        return (
          !itemProps.showHLDD && (
            <div className={"hiddenPdf"}>
              <DeboundInput
                label={`<b>Ghi chú: </b>`}
                rows={1}
                readOnly={_isDisable}
                value={formatText(ngayDieuTri[key]) || ""}
                onChange={onChange(
                  key,
                  ngayDieuTri,
                  index,
                  toDieuTriId,
                  isEditDienBien
                )}
                type="multipleline"
                lineHeightText={1.5}
                fontSize={12}
                minHeight={12 + 6}
                disabled={_isDisable}
                markSpanRow={itemProps.markSpanRow}
              />
            </div>
          )
        );
      case "cheDoAn":
        return (
          !itemProps.showHLDD &&
          chiDinhTuLoaiDichVu != LOAI_DICH_VU.KHAM && (
            <div className={"hiddenPdf"}>
              <DeboundInput
                label={`<b>${t("editor.cheDoAn")}: </b>`}
                rows={1}
                readOnly={_isDisable}
                value={formatText(ngayDieuTri.cheDoAn)}
                onChange={onChange(
                  "cheDoAn",
                  ngayDieuTri,
                  index,
                  toDieuTriId,
                  isEditDienBien
                )}
                type="multipleline"
                lineHeightText={1.5}
                fontSize={12}
                minHeight={12 + 6}
                disabled={_isDisable}
                markSpanRow={itemProps.markSpanRow}
              />
            </div>
          )
        );
      case "cheDoChamSoc":
        return (
          !itemProps.showHLDD &&
          chiDinhTuLoaiDichVu != LOAI_DICH_VU.KHAM && (
            <div className={"flex-l hiddenPdf che-do-cham-soc"}>
              <b>{t("editor.cheDoChamSoc")}: </b>{" "}
              {_isDisable ? (
                ngayDieuTri.tenCheDoChamSoc
              ) : (
                <DropDownList
                  component={{
                    props: {
                      type: "onlyOne",
                      checkList: listChamSoc,
                      fieldName: "cheDoChamSocId",
                      listType: 2,
                      noLabel: true,
                      minHeight: "10",
                      fontSize: "12",
                      lineHeight: "1.5",
                      markSpanRow: itemProps.markSpanRow,
                    },
                  }}
                  blockWidth="150px"
                  className={"drop-list"}
                  form={{
                    cheDoChamSocId: ngayDieuTri.cheDoChamSocId,
                  }}
                  disable={_isDisable}
                  formChange={{
                    cheDoChamSocId: (value) => {
                      onChange(
                        "cheDoChamSocId",
                        ngayDieuTri,
                        index,
                        toDieuTriId,
                        isEditDienBien
                      )(value);
                    },
                  }}
                />
              )}
            </div>
          )
        );
      case "dichVuCls":
        const showTenNhomDichVuCap2 =
          tuyChinhTruongSapXepYLenh.find(
            (item) =>
              item.parentFieldName === "dichVuCls" &&
              item.fieldName === "tenNhomDichVuCap2"
          )?.show ?? true;
        return (
          itemProps.viTriDichVu === 2 &&
          renderListDv(ngayDieuTri, {
            gopDv,
            showTenNhomDichVuCap2,
            hienThiDichVuKham: itemProps.hienThiDichVuKham,
            tachDongDvCLS: itemProps.tachDongDvCLS,
            hienThiDvktNgungYLenhKhongThucHien:
              itemProps.hienThiDvktNgungYLenhKhongThucHien,
            listTrangThaiHoan,
            hienThiTextTrangThaiHoanNgungYLenhKhongThucHienDvkt:
              itemProps.hienThiTextTrangThaiHoanNgungYLenhKhongThucHienDvkt,
          })
        );
      case "mau":
        return renderMau({
          ngayDieuTri,
          listNhomMau,
          listDonViTocDoTruyen,
          hienThiCachTruyenChePhamMau: itemProps.hienThiCachTruyenChePhamMau,
        });
      case "phanLoaiPHCN":
        return dsPhanLoaiPHCN?.length ? (
          <span
            style={{
              marginTop: 30,
              paddingBottom: 30,
            }}
          >
            <b>{t("editor.phanLoaiPHCN")}: </b>
            {dsPhanLoaiPHCN.join(", ")}
          </span>
        ) : (
          ""
        );
      case "dsDvKt":
        return (
          itemProps.viTriDichVu === 2 &&
          renderListDvNgoaiTru(ngayDieuTri?.dsDvKt || [], {
            hienThiDvktNgungYLenhKhongThucHien,
            hienThiTextTrangThaiHoanNgungYLenhKhongThucHienDvkt,
            listTrangThaiHoan,
          })
        );
      case "dsThuoc":
        return ngayDieuTri?.dsThuoc?.length ? (
          <span
            style={{
              marginTop: 30,
              paddingBottom: 30,
            }}
          >
            <b>{t("quanLyNoiTru.thuoc")}: </b>
            {ngayDieuTri?.dsThuoc.map((item, index) => (
              <span key={index}>
                {isShowSoLuongThuoc ? <b>{item.tenDichVu}</b> : item.tenDichVu}
                {isShowSoLuongThuoc && (
                  <>
                    {" x "}
                    {item.soLuong}
                    {item.tenDonViTinh && ` (${item.tenDonViTinh})`}
                  </>
                )}
                {index < ngayDieuTri.dsThuoc.length - 1 && ", "}
              </span>
            ))}
          </span>
        ) : (
          ""
        );
      case "dsThuoc2":
        return ngayDieuTri?.dsThuoc2?.length ? (
          <span
            style={{
              marginTop: 30,
              paddingBottom: 30,
            }}
          >
            <b>{t("quanLyNoiTru.thuoc")}: </b>
            {ngayDieuTri?.dsThuoc2.map((item, index) => (
              <div key={index}>
                <b>{item.tenDichVu}</b>
                <>
                  {" x "}
                  {item.soLuong}
                  {item.tenDonViTinh && ` (${item.tenDonViTinh})`}
                </>
                <>{"  "}{item.tenLieuDung}</>
              </div>
            ))}
          </span>
        ) : (
          ""
        );
      case "dsCls":
        return renderListDvNgoaiTru(ngayDieuTri?.dsCls || []);
      case "dsAllThuoc":
        let _thuocs = [
          ...(ngayDieuTri.dsThuocDaChiDinh || []),
          ...(ngayDieuTri.dsThuoc || []),
          ...(ngayDieuTri.dsThuocChiDinhNgoai || []),
          ...(ngayDieuTri.dsVacXin || []),
        ];

        return _thuocs?.length ? (
          <span
            style={{
              marginTop: 30,
              paddingBottom: 30,
            }}
          >
            <b>{t("quanLyNoiTru.thuoc")}: </b>
            {_thuocs.map((item, index) => (
              <span key={index}>
                {isShowSoLuongThuoc ? <b>{item.tenDichVu}</b> : item.tenDichVu}
                {isShowSoLuongThuoc && (
                  <>
                    {" x "}
                    {item.soLuong}
                    {item.tenDonViTinh && ` (${item.tenDonViTinh})`}
                  </>
                )}
                <i style={{ fontWeight: "normal", marginLeft: 2 }}>
                  {item.soLuongHuy
                    ? `  (${t("common.huy")} ${item.soLuongHuy} ${
                        item.tenDonViTinh || ""
                      }${item.lyDoHuy ? `, ${item.lyDoHuy}` : ""})`
                    : ""}
                </i>
                <i style={{ fontWeight: "normal", marginLeft: 2 }}>
                  {`${item.tenLieuDung || ""} ${
                    item.cachDung ? `${item.cachDung}` : ""
                  }`}
                  <span>
                    {!!item.ghiChu && `.${t("editor.ghiChu")}: ${item.ghiChu}`}
                  </span>
                </i>
                {index < _thuocs.length - 1 && ", "}
              </span>
            ))}
          </span>
        ) : (
          ""
        );
      case "cheDoTheoDoi":
        return (
          !itemProps.showHLDD &&
          chiDinhTuLoaiDichVu != LOAI_DICH_VU.KHAM && (
            <div className={"hiddenPdf"}>
              <DeboundInput
                label={`<b>${t("editor.cheDoTheoDoi")}: </b>`}
                rows={1}
                readOnly={_isDisable}
                value={formatText(ngayDieuTri.cheDoTheoDoi)}
                onChange={onChange(
                  "cheDoTheoDoi",
                  ngayDieuTri,
                  index,
                  toDieuTriId,
                  isEditDienBien
                )}
                type="multipleline"
                lineHeightText={1.5}
                fontSize={12}
                minHeight={12 + 6}
                disabled={_isDisable}
                markSpanRow={itemProps.markSpanRow}
              />
            </div>
          )
        );
      case "dsThuocNgoaiTru":
        return (
          <div>
            {_isDisable ? (
              ngayDieuTri.listThuoc?.map((item) => {
                return <RenderThuocs item={item} />;
              })
            ) : (
              <>
                <div className={"flex-l hiddenPdf"}>
                  <b>{t("common.thuoc")}: </b>{" "}
                  <DropDownList
                    component={{
                      props: {
                        type: "multiple",
                        checkList: ngayDieuTri.listThuoc,
                        fieldName: "dsDichVuThuocId",
                        listType: 2,
                        noLabel: true,
                        minHeight: "10",
                        fontSize: "12",
                        lineHeight: "1.5",
                      },
                    }}
                    blockWidth="150px"
                    className={"drop-list"}
                    form={{
                      dsDichVuThuocId:
                        state?.dsDichVuThuocId[index] ||
                        ngayDieuTri.dsDichVuThuocId,
                    }}
                    disable={_isDisable}
                    formChange={{
                      dsDichVuThuocId: (value) => {
                        onChange(
                          "dsDichVuThuocId",
                          ngayDieuTri,
                          index,
                          toDieuTriId,
                          isEditDienBien
                        )(value);
                      },
                    }}
                  />
                </div>

                {ngayDieuTri?.listThuoc
                  ?.filter((x) =>
                    (
                      state?.dsDichVuThuocId[index]?.map(Number) ||
                      ngayDieuTri.dsDichVuThuocId
                    )?.includes(x.id)
                  )
                  ?.map((item) => {
                    return <RenderThuocs item={item} />;
                  })}
              </>
            )}
          </div>
        );
      case "dsDvKtNgoaiTru":
        return (
          <>
            <div className={"flex-l hiddenPdf"}>
              <b>{t("quanLyNoiTru.dvNoiTru.dvkt")}: </b>{" "}
              {_isDisable ? (
                renderListDvNgoaiTru(ngayDieuTri?.listDvKt || [], {
                  hienThiDvktNgungYLenhKhongThucHien,
                  hienThiTextTrangThaiHoanNgungYLenhKhongThucHienDvkt,
                  listTrangThaiHoan,
                })
              ) : (
                <DropDownList
                  component={{
                    props: {
                      type: "multiple",
                      checkList: ngayDieuTri.listDvKt,
                      fieldName: "dsDichVuKyThuatId",
                      listType: 2,
                      noLabel: true,
                      minHeight: "10",
                      fontSize: "12",
                      lineHeight: "1.5",
                    },
                  }}
                  blockWidth="150px"
                  className={"drop-list"}
                  form={{
                    dsDichVuKyThuatId:
                      state?.dsDichVuKyThuatId[index] ||
                      ngayDieuTri.dsDichVuKyThuatId,
                  }}
                  disable={_isDisable}
                  formChange={{
                    dsDichVuKyThuatId: (value) => {
                      onChange(
                        "dsDichVuKyThuatId",
                        ngayDieuTri,
                        index,
                        toDieuTriId,
                        isEditDienBien
                      )(value);
                    },
                  }}
                />
              )}
            </div>
            <div>
              {renderListDvNgoaiTru(
                ngayDieuTri?.listDvKt?.filter((x) =>
                  (
                    state?.dsDichVuKyThuatId[index]?.map(Number) ||
                    ngayDieuTri.dsDichVuKyThuatId
                  )?.includes(x.id)
                ) || [],
                {
                  hienThiDvktNgungYLenhKhongThucHien,
                  hienThiTextTrangThaiHoanNgungYLenhKhongThucHienDvkt,
                  listTrangThaiHoan,
                }
              )}
            </div>
          </>
        );
      case "chePhamDinhDuong":
        return ngayDieuTri?.dsChePham?.length ? (
          <div>
            <b>{t("editor.chePhamDinhDuong")}: </b>
            {ngayDieuTri?.dsChePham.map((item, index) => (
              <span key={index}>
                {item.tenDichVu}
                {item.hamLuong && ` (${item.hamLuong})`}
                {item.soLuong && ` ${item.soLuong}`}
                {item.tenDonViTinh && ` (${item.tenDonViTinh})`}
                {index < ngayDieuTri.dsChePham.length - 1 && ", "}
                <div>
                  <i style={{ fontWeight: "normal", marginLeft: 2 }}>
                    {`${item.tenLieuDung || ""} ${
                      item.cachDung ? `${item.cachDung}` : ""
                    }  ${item.thoiDiem ? `${item.thoiDiem}` : ""}  ${
                      !!item.ghiChu && `.${t("common.luuY")}: ${item.ghiChu}`
                    }`}
                  </i>
                </div>
              </span>
            ))}
          </div>
        ) : (
          ""
        );
      case "suatAn":
        return ngayDieuTri?.dsSuatAn?.length ? (
          <div>
            <b>{t("common.suatAn")}: </b>
            {ngayDieuTri?.dsSuatAn.map((item, index) => (
              <span key={index}>
                {item.tenDichVu}
                {item.tenLoaiBuaAn && ` ${item.tenLoaiBuaAn}`}
                {item.soLuong && ` ${item.soLuong}`}
                {item.tenDonViTinh && ` (${item.tenDonViTinh})`}
                {index < ngayDieuTri.dsSuatAn.length - 1 && ", "}
              </span>
            ))}
          </div>
        ) : (
          ""
        );
      default:
        break;
    }
  };

  const renderYLenhSanKhoa = (ngayDieuTri, index, isReadOnly) => {
    //nếu có thiết lập khóa phiếu sau ký => disabled những tờ điều trị đã được ký
    const _isDisable =
      isDisable || (khoaPhieuSauKhiKy && !!ngayDieuTri?.lichSuKy?.id);

    const localProps = {
      ngayDieuTri,
      index,
      onChange,
      onFormChange,
      isDisable: _isDisable,
      isReadOnly,
      renderHuongDieuTri: renderContent({
        key: "dsAllThuoc",
        ngayDieuTri,
        index,
        isShowSoLuongThuoc: true,
      }),
    };

    return <YLenhSanKhoa {...localProps} />;
  };

  const renderYLenhSoSinh = (ngayDieuTri, index, isReadOnly) => {
    //nếu có thiết lập khóa phiếu sau ký => disabled những tờ điều trị đã được ký
    const _isDisable =
      isDisable || (khoaPhieuSauKhiKy && !!ngayDieuTri?.lichSuKy?.id);

    const localProps = {
      ngayDieuTri,
      index,
      onChange,
      onFormChange,
      isDisable: _isDisable,
      isReadOnly,
      renderHuongDieuTri: renderContent({
        key: "dsAllThuoc",
        ngayDieuTri,
        index,
        isShowSoLuongThuoc: true,
      }),
      mode,
    };

    return <YLenhSoSinh {...localProps} />;
  };

  const renderToSo = (data) => {
    const first = Math.min(...data.map((x) => x.stt));
    const last = Math.max(...data.map((x) => x.stt));
    const text = `${first} - ${last}`;
    return text;
  };

  const isCheckKyBacSi =
    !listBacSi?.some((x) => x.id === auth.nhanVienId) &&
    itemProps.bacSiKyToDieuTri;
  return (
    <Main
      className="to-dieu-tri"
      breakPage={toDieuTris.length > 1}
      disable={isDisable}
      data-value-stt={TO_DIEU_TRI_IN_THONG_TIN_HANH_CHINH}
      key={toDieuTri.id}
      itemProps={itemProps}
    >
      <GlobalStyle></GlobalStyle>
      <div className="header-form">
        <div
          data-type="line"
          className="line flex"
          style={{
            justifyContent: "space-between",
            marginBottom: 10,
          }}
        >
          <div
            style={{
              width: itemProps.isToDieuTriPS
                ? 150
                : itemProps.isPSHN
                ? 310
                : 250,
              justifyContent: "flex-start",
            }}
            className="text-center title-left"
          >
            <div>
              {itemProps.showLogo === false ? (
                <></>
              ) : (
                <img
                  src={logoAnh || state?.logoBv || ""}
                  style={{ width: 80, height: 60, objectFit: "scale-down" }}
                ></img>
              )}
            </div>

            <div>
              {itemProps.showTitleLeft1 && <div>{form?.tieuDeTrai1}</div>}
              {itemProps.showTitleLeft2 && (
                <div>
                  <b>{form?.tieuDeTrai2}</b>
                </div>
              )}
            </div>
          </div>
          <div
            data-type="line"
            className="ten-phieu"
            style={{
              flex: 1,
              alignItems: "center",
              fontSize: itemProps.isToDieuTriPS ? 18 : 22,
            }}
          >
            <div
              style={{
                textTransform: "uppercase",
                marginRight: itemProps.isPSHN ? 75 : 0,
              }}
            >
              {itemProps?.tieuDeCuaPhieu ||
                t("editor.toDieuTri.phieuTheoDoiDieuTri")}
            </div>
            <div
              className="fs16 flex"
              style={{
                marginRight: itemProps.isPSHN ? 75 : 0,
              }}
            >
              Tờ số
              <div className="stt" width={60}>
                <span contentEditable={true} className="number-stt">
                  {dsToDieuTri?.length > 1
                    ? itemProps.hienThiGiaTriToSoNhieuNgay
                      ? renderToSo(toDieuTri?.ngayDieuTris)
                      : ""
                    : toDieuTri?.ngayDieuTris[0]?.stt}
                </span>
                <span className="dot">
                  {new Array(5).fill("").map((item, index) => {
                    return (
                      <svg
                        key={index}
                        xmlns="http://www.w3.org/2000/svg"
                        version="1.1"
                        viewBox="0 0 6 6"
                      >
                        <circle cx="3" cy="3" r="1.5" fill="black" />
                      </svg>
                    );
                  })}
                </span>
              </div>
            </div>
          </div>
          <div
            style={
              itemProps.isPSHN
                ? { width: 200, lineHeight: 1 }
                : {
                    width: 175,
                  }
            }
          >
            {(itemProps.positionHeaderRight || DS_TIEU_DE_PHAI).map((item) => {
              if (item.show) {
                return (
                  <div key={item.fieldName}>
                    {itemProps.isPSHN
                      ? TEN_TIEU_DE_TRAI_BY_FIELDNAME_SAN_HN[item.fieldName]
                        ? `${
                            TEN_TIEU_DE_TRAI_BY_FIELDNAME_SAN_HN[item.fieldName]
                          }: `
                        : ""
                      : TEN_TIEU_DE_TRAI_BY_FIELDNAME[item.fieldName]
                      ? `${TEN_TIEU_DE_TRAI_BY_FIELDNAME[item.fieldName]}: `
                      : ""}
                    {item.fieldName === "maSoBm" ? (
                      itemProps["maSoBm"] || item.defaultValue
                    ) : item.fieldName === "barcode" ? (
                      <Barcode
                        component={{
                          props: {
                            label: "Mã HS",
                            width: 175,
                            height: 35,
                            contentAlign: "left",
                            fieldName: "maHoSo",
                            noLabel: true,
                          },
                        }}
                        mode={mode}
                        form={form}
                      />
                    ) : (
                      get(form, item.fieldName, "")
                    )}
                  </div>
                );
              } else {
                return <></>;
              }
            })}
          </div>
        </div>
        {renderThongTinChung({
          form: {
            form,
            toDieuTri: gopToDieuTriNhieuNgay
              ? {
                  ...toDieuTri,
                  tenPhong: toDieuTri?.ngayDieuTris[0]?.tenPhong,
                  soHieuGiuong: toDieuTri?.ngayDieuTris[0]?.soHieuGiuong,
                  cdChinh: toDieuTri?.ngayDieuTris[0]?.cdChinh,
                  cdKemTheo: toDieuTri?.ngayDieuTris[0]?.cdKemTheo,
                  moTa: toDieuTri?.ngayDieuTris[0]?.moTa,
                  cdSoBo: toDieuTri?.ngayDieuTris[0]?.cdSoBo,
                }
              : {
                  ...toDieuTri,
                },
          },
          isDisable,
          itemProps,
          onChange,
          maBaoCao,
        })}

        {isTDTSoSinhMemo && (
          <Row>
            <Col span={24}>
              <b>Họ tên mẹ: </b>
              {form?.thongTinSanPhu?.tenNb || ""}
            </Col>
            <Col span={24}>
              <b>Mã số mẹ: </b>
              {form?.thongTinSanPhu?.maNb || ""}
            </Col>
          </Row>
        )}
      </div>

      <table data-type="table-to-dieu-tri">
        <thead className="table-head">
          <tr>
            <td className="text-center text-uppercase">
              {itemProps.tieuDeThoiGian || t("editor.toDieuTri.thoiGian")}
            </td>
            <td className="text-center text-uppercase">
              {itemProps.tieuDeDienBienBenh ||
                t("editor.toDieuTri.dienBienBenh")}
            </td>
            <td className="text-center">
              <div
                className="flex-center text-uppercase"
                style={{ height: "100%" }}
              >
                {itemProps.tieuDeChiDinh || t("editor.toDieuTri.chiDinh")}{" "}
                {!isDisable && !headless && (
                  <Popover content={content(toDieuTri.ngayDieuTris[0], 0)}>
                    <SVG.IcAdd className="icon" />
                  </Popover>
                )}
              </div>
            </td>
            {itemProps.showHLDD && (
              <td className="text-center text-uppercase">
                {itemProps.tieuDeDanDoHLDD || t("editor.toDieuTri.danDoHLDD")}
              </td>
            )}
          </tr>
        </thead>

        <tbody className="table-tbody">
          {(toDieuTri.ngayDieuTris || []).map((ngayDieuTri, index) => {
            console.log("ngayDieuTri", ngayDieuTri);
            const dsPhanLoaiPHCN = (ngayDieuTri.dsNbPhcn || [])
              .filter(
                (item) =>
                  moment(item.thoiGianDangKy).format("DD/MM/YYYY") ===
                  moment(ngayDieuTri.thoiGianYLenh).format("DD/MM/YYYY")
              )
              .map((item) =>
                (item.dsPhanLoaiPhcn || []).map((el) => el.ten).join(", ")
              );
            const isSign =
              get(ngayDieuTri, "chuKyBacSiDieuTri.nguoiKyId") ||
              ngayDieuTri.lichSuKyId;
            const key = ngoaiTru ? "thoiGianThucHien" : "thoiGianYLenh";

            //kiểm tra tờ điều trị đầu tiên có khoa chỉ định có tính chất TỜ ĐIỀU TRỊ SẢN KHOA (160) || TỜ ĐIỀU TRỊ SƠ SINH (170)
            const { isToDieuTriSanKhoa, isToDieuTriSoSinh } =
              checkChuyenKhoaToDieuTri(
                ngayDieuTri.id,
                ngayDieuTri.khoaChiDinhId
              );
            const chuKy = ngayDieuTri?.lichSuKy?.dsChuKy?.find(
              (x) => x.chuKySo === itemProps.capKyTruongKhoa
            );

            return (
              <NgayDieuTriStyled
                isSign={isSign}
                key={index}
                style={
                  !index
                    ? { pageBreakInside: "unset", pageBreakAfter: "unset" }
                    : {}
                }
              >
                <td className="text-center" style={{ width: "50px" }}>
                  <div>
                    {maBaoCao === "EMR_BA077.2" ? (
                      <>
                        <div>Từ</div>
                        <div>
                          {ngayDieuTri?.thoiGianDangKy &&
                            moment(ngayDieuTri?.thoiGianDangKy)
                              .utcOffset("+0700")
                              .format("DD/MM")}
                        </div>
                        <div>
                          {ngayDieuTri?.thoiGianDangKy &&
                            moment(ngayDieuTri?.thoiGianDangKy)
                              .utcOffset("+0700")
                              .format("HH:mm")}
                        </div>
                        {ngayDieuTri.thoiGianHoanThanh ? (
                          <>
                            <div>đến</div>
                            <div>
                              {moment(ngayDieuTri?.thoiGianHoanThanh)
                                .utcOffset("+0700")
                                .format("DD/MM")}
                            </div>
                            <div>
                              {moment(ngayDieuTri?.thoiGianHoanThanh)
                                .utcOffset("+0700")
                                .format("HH:mm")}
                            </div>
                          </>
                        ) : null}
                      </>
                    ) : (
                      <>
                        <div>
                          {moment(ngayDieuTri?.[key])
                            .utcOffset("+0700")
                            .format("dddd")}
                        </div>
                        <div>
                          {itemProps?.hienThiNam
                            ? moment(ngayDieuTri?.[key])
                                .utcOffset("+0700")
                                .format("DD/MM/YYYY")
                            : moment(ngayDieuTri?.[key])
                                .utcOffset("+0700")
                                .format("DD/MM")}
                        </div>
                        {hienThiGio && (
                          <div>
                            {moment(ngayDieuTri?.[key])
                              .utcOffset("+0700")
                              .format("HH:mm")}
                          </div>
                        )}
                        {ngoaiTru && (
                          <div className="icon-add">
                            <Popover content={content(ngayDieuTri, index)}>
                              <SVG.IcAdd />
                            </Popover>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </td>
                <td
                  style={{
                    width:
                      isToDieuTriSanKhoa || isToDieuTriSoSinh
                        ? "400px"
                        : !itemProps.showHLDD
                        ? "300px"
                        : "250px",
                  }}
                >
                  {dienBienBenh(ngayDieuTri, index, false, ngayDieuTri?.id)}
                </td>
                <td
                  style={{
                    width:
                      !itemProps.showHLDD ||
                      isToDieuTriSanKhoa ||
                      isToDieuTriSoSinh
                        ? "unset"
                        : "250px",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      marginLeft:
                        isToDieuTriSanKhoa || isToDieuTriSoSinh
                          ? "5px"
                          : "25px",
                    }}
                  >
                    {index > 0 && !headless < 0 && !ngoaiTru && (
                      <Popover content={content(ngayDieuTri, index)}>
                        <SVG.IcAdd className="icon add-second" />
                      </Popover>
                    )}
                    {}

                    {
                      <span>
                        {isToDieuTriSanKhoa
                          ? renderYLenhSanKhoa(ngayDieuTri, index)
                          : isToDieuTriSoSinh
                          ? renderYLenhSoSinh(ngayDieuTri, index)
                          : (ngoaiTru
                              ? [
                                  { fieldName: "boSung", show: true },
                                  { fieldName: "thuoc", show: true },
                                  { fieldName: "dsDvKt", show: true },
                                  { fieldName: "ghiChu", show: true },
                                ]
                              : listSapXepYLenh
                            )
                              .filter((item) => item.show)
                              .map((item) => {
                                return renderContent({
                                  key: item.fieldName,
                                  ngayDieuTri,
                                  index,
                                  dsPhanLoaiPHCN,
                                  toDieuTriId: ngayDieuTri?.id,
                                });
                              })}
                      </span>
                    }
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-around",
                      }}
                    >
                      {hienThiChanKyTruongKhoa && (
                        <span
                          style={{
                            alignSelf: "center",
                            marginTop: !!dsPhanLoaiPHCN?.length ? 0 : 30,
                            paddingBottom: !!dsPhanLoaiPHCN?.length ? 0 : 30,
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                          }}
                          className="sign"
                        >
                          <ImageSign
                            component={{
                              props: {
                                fieldName: "chuKy2",
                                fontSize: itemProps.fontSizeCa || 12,
                                alignCa: "center",
                                contentColor:
                                  itemProps.contentColorTruongKhoa || "red",
                                width: 150,
                                height: 100,
                                viTriCa: itemProps.viTriCaTruongKhoa,
                                showCa: itemProps.showCaTruongKhoa,
                                viTriAnhCa: itemProps.viTriAnhCaTruongKhoa,
                                widthCa: itemProps.widthCaTruongKhoa,
                                allowReset:
                                  itemProps.allowResetTruongKhoa == undefined
                                    ? true
                                    : itemProps.allowResetTruongKhoa,
                                capKy: itemProps.capKyTruongKhoa || 1,
                                loaiKy: itemProps.loaiKyTruongKhoa,
                                showPatientSign:
                                  itemProps.showPatientSignTruongKhoa,
                                alwaysShow:
                                  chanKyBaoCao?.dieuKienKy2?.includes(20) ||
                                  chanKyBaoCao?.dieuKienKy1?.includes(20),
                                disabled: mode === MODE.config,
                                dataSign: {
                                  kySo: ngayDieuTri.kySo,
                                  soPhieu:
                                    ngayDieuTri.soPhieu || ngayDieuTri.id,
                                  lichSuKyId: ngayDieuTri.lichSuKyId,
                                  tenChanKy: ngayDieuTri.dsTenChanKy2?.length
                                    ? ngayDieuTri.dsTenChanKy2[0]
                                    : "",
                                  chuKySo: 1,
                                  baoCaoId: maBaoCao,
                                  nbDotDieuTriId: ngayDieuTri.nbDotDieuTriId,
                                  trangThaiKy: ngayDieuTri.trangThaiChanKy2,
                                  id: ngayDieuTri.id,
                                  maBaoCao: maBaoCao,
                                },
                              },
                            }}
                            mode={mode}
                            form={ngayDieuTri}
                          ></ImageSign>
                          {hienThiTenTruongKhoa && (
                            <b>
                              {ngayDieuTri?.lichSuKy?.dsChuKy?.find(
                                (x) => x.chuKySo === itemProps.capKyTruongKhoa
                              )?.vietTatHocHamHocVi || ""}{" "}
                              {(hienThiTenDayDu
                                ? chuKy?.tenNguoiKy
                                : chuKy?.vietTatTenNguoiKy) || ""}
                            </b>
                          )}
                        </span>
                      )}
                      <span
                        style={{
                          alignSelf: "center",
                          marginTop: !!dsPhanLoaiPHCN?.length ? 0 : 30,
                          paddingBottom: !!dsPhanLoaiPHCN?.length ? 0 : 30,
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                        }}
                        className="sign"
                      >
                        <ImageSign
                          component={{
                            props: {
                              fieldName: "chuKy1",
                              fontSize: itemProps.fontSizeCa || 12,
                              alignCa: "center",
                              contentColor: itemProps.contentColor || "red",
                              width: 150,
                              height: 100,
                              viTriCa: itemProps.viTriCa,
                              showCa: itemProps.showCa,
                              viTriAnhCa: itemProps.viTriAnhCa,
                              widthCa: itemProps.widthCa,
                              allowReset:
                                itemProps.allowReset == undefined
                                  ? true
                                  : itemProps.allowReset,
                              capKy: itemProps.capKy || 1,
                              loaiKy: itemProps.loaiKy,
                              showPatientSign: itemProps.showPatientSign,
                              disabled: mode === MODE.config,
                              isCheckKyBacSi: isCheckKyBacSi,
                              dataSign: {
                                kySo: ngayDieuTri.kySo,
                                soPhieu: ngayDieuTri.soPhieu || ngayDieuTri.id,
                                lichSuKyId: ngayDieuTri.lichSuKyId,
                                tenChanKy: ngayDieuTri.dsTenChanKy1?.length
                                  ? ngayDieuTri.dsTenChanKy1[0]
                                  : "",
                                chuKySo: 2,
                                baoCaoId: maBaoCao,
                                nbDotDieuTriId: ngayDieuTri.nbDotDieuTriId,
                                trangThaiKy: ngayDieuTri.trangThaiChanKy1,
                                id: ngayDieuTri.id,
                                maBaoCao: maBaoCao,
                              },
                            },
                          }}
                          mode={mode}
                          form={ngayDieuTri}
                        ></ImageSign>

                        {hienBSDieuTri && (
                          <b>
                            {ngayDieuTri?.vietTatHhhvBsDieuTri || ""}{" "}
                            {ngayDieuTri?.tenBacSiDieuTri || ""}
                          </b>
                        )}
                      </span>
                    </div>
                  </div>
                </td>
                {!itemProps.hideHLDD && (
                  <td>
                    <div className={"hiddenPdf"}>
                      <DeboundInput
                        label={"<b>Chế độ ăn: </b>"}
                        rows={1}
                        readOnly={isDisable}
                        value={formatText(ngayDieuTri.cheDoAn)}
                        onChange={onChange(
                          "cheDoAn",
                          ngayDieuTri,
                          index,
                          ngayDieuTri?.id
                        )}
                        type="multipleline"
                        lineHeightText={1.5}
                        fontSize={12}
                        minHeight={12 + 6}
                        disabled={isDisable}
                        markSpanRow={itemProps.markSpanRow}
                      ></DeboundInput>
                    </div>
                    <div className="flex hiddenPdf">
                      <b>Chế độ chăm sóc: </b>
                      {isDisable ? (
                        ngayDieuTri.tenCheDoChamSoc
                      ) : (
                        <DropDownList
                          component={{
                            props: {
                              type: "onlyOne",
                              checkList: listChamSoc,
                              fieldName: "cheDoChamSocId",
                              listType: 2,
                              noLabel: true,
                              minHeight: "10",
                              fontSize: "12",
                              lineHeight: "1.5",
                            },
                          }}
                          blockWidth="150px"
                          className={"drop-list"}
                          form={{
                            cheDoChamSocId: ngayDieuTri.cheDoChamSocId,
                          }}
                          formChange={{
                            cheDoChamSocId: (value) => {
                              onChange(
                                "cheDoChamSocId",
                                ngayDieuTri,
                                index,
                                ngayDieuTri?.id
                              )(value);
                            },
                          }}
                        />
                      )}
                    </div>
                    <div className="hiddenPdf">
                      <DeboundInput
                        label={"<b>Ghi chú: </b>"}
                        rows={1}
                        readOnly={isDisable}
                        value={formatText(ngayDieuTri.ghiChu) || ""}
                        onChange={onChange(
                          "ghiChu",
                          ngayDieuTri,
                          index,
                          ngayDieuTri?.id
                        )}
                        type="multipleline"
                        lineHeightText={1.5}
                        fontSize={12}
                        minHeight={12 + 6}
                        disabled={isDisable}
                        markSpanRow={itemProps.markSpanRow}
                      ></DeboundInput>
                    </div>
                    <div className={"hiddenPdf"}>
                      <DeboundInput
                        label={"<b>Chế độ theo dõi: </b>"}
                        rows={1}
                        readOnly={isDisable}
                        value={formatText(ngayDieuTri.cheDoTheoDoi)}
                        onChange={onChange(
                          "cheDoTheoDoi",
                          ngayDieuTri,
                          index,
                          ngayDieuTri?.id
                        )}
                        type="multipleline"
                        lineHeightText={1.5}
                        fontSize={12}
                        minHeight={12 + 6}
                        disabled={isDisable}
                        markSpanRow={itemProps.markSpanRow}
                      ></DeboundInput>
                    </div>
                  </td>
                )}
              </NgayDieuTriStyled>
            );
          })}
          {(toDieuTri.ngayDieuTris || []).map((ngayDieuTri, index) => {
            if (ngayDieuTri?.dsDienBienBenh?.length) {
              return ngayDieuTri.dsDienBienBenh.map((x, idx) => {
                return renderThemMoiToDieuTriNgoaiTru({
                  index: idx,
                  onChange,
                  ngayDieuTri: x,
                  dienBienBenh,
                  content,
                  renderContent,
                  mode,
                  hienBSDieuTri,
                  listChamSoc,
                  listSapXepYLenh,
                  itemProps,
                  dienBienBenhNgoaiTru,
                  onDeleteToDieuTri,
                  ngoaiTru,
                  thoiGianThucHien:
                    state.thoiGianThucHien?.[idx] ?? x?.thoiGianThucHien,
                  maBaoCao,
                  toDieuTriId: ngayDieuTri?.id,
                  isEditDienBien: true,
                });
              });
            }
          })}
        </tbody>
      </table>
      {(ngoaiTru || maBaoCao === "EMR_BA077.2") &&
        itemProps?.themXoaToDieuTri && (
          <SVG.IcAdd
            className="ic-remove"
            onClick={onAddToDieuTri(toDieuTri)}
          ></SVG.IcAdd>
        )}
    </Main>
  );
};

export default DanhSachToDieuTri;
