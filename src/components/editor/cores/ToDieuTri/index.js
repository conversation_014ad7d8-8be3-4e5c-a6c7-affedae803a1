import React, {
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import T from "prop-types";
import { Button } from "antd";
import { GlobalStyle } from "./styled";
import { SettingOutlined } from "@ant-design/icons";
import { MODE } from "utils/editor-utils";
import DanhSachToDieuTri from "./DanhSachToDieuTri";
import { cloneDeep, groupBy } from "lodash";
import moment from "moment";
import { useListAll, useStore, useThietLap } from "hooks";
import { LOAI_DICH_VU, THIET_LAP_CHUNG } from "constants/index";
import {
  LIST_CHI_SO_SONG,
  LIST_SAP_XEP_DIEN_BIEN_BENH,
  LIST_SAP_XEP_Y_LENH,
  LIST_TUY_CHINH_TRUONG_SAP_XEP_Y_LENH,
} from "./DanhSachToDieuTri/ultils";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import { useQueryString } from "hooks";
import nbDvKhamProvider from "data-access/nb-dv-kham-provider";
import { useParams } from "react-router-dom";

const ToDieuTri = forwardRef((props, ref) => {
  const { component, mode, form, formChange } = props;
  const init = useDispatch().component.init;
  const { baoCaoId, ngoaiTru = false } = getAllQueryString();
  const [nbDotDieuTriId] = useQueryString("nbDotDieuTriId");
  const { id, maBaoCao } = useParams();
  const [listAllChiSoSong] = useListAll("chiSoSong", {}, true);
  const [dsToDieuTriMemo, setDsToDieuTriMemo] = useState([]);
  const {
    vitalSigns: { getDataVitalSigns },
    files: { getInfoBaoCao, getDuLieuTheoLichSuKyId },
    toDieuTri: { getToDieuTri },
    nhanVien: { getListBacSi },
  } = useDispatch();

  const refId = useRef([]);
  const { values } = useSelector((state) => state.vitalSigns);
  const listToDieuTriByNb = useStore("toDieuTri.listToDieuTri", []);
  const itemProps = component.props || {};
  const refValues = useRef();
  useImperativeHandle(ref, () => ({}));

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };

  useEffect(() => {
    if (!baoCaoId && mode !== MODE.config) {
      const _ma = ngoaiTru ? "EMR_BA077.1" : "EMR_BA077";
      getInfoBaoCao(_ma).then((s) => {
        window.history.replaceState(
          null,
          "",
          `${window.location.href}&baoCaoId=${s}`
        );
      });
    }
    getListBacSi({
      dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
      active: true,
      page: "",
      size: "",
    });
  }, []);

  const listCss = useMemo(() => {
    let data = itemProps?.listCss || LIST_CHI_SO_SONG;
    (listAllChiSoSong || [])
      .map((css) => ({
        ...css,
        fieldName: css.ma,
        show: true,
      }))
      .forEach((item) => {
        if (!data.some((el) => el.fieldName === item.ma)) {
          data.push(item);
        }
      });

    return data.filter((item) => item.show);
  }, [listAllChiSoSong, itemProps?.listCss]);

  const [MA_NHOM_DICH_VU_CAP1_TT] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_TT
  );

  const dsChiSoSong = useMemo(() => {
    if (values) {
      const a = (values || []).map((item) => {
        return {
          ...item,
          date: moment(item.thoiGianThucHien).format("DD/MM/YYYY"),
          huyetAp: `${item.huyetApTamThu || ""}${
            item.huyetApTamTruong ? `/${item.huyetApTamTruong}` : ""
          }`,
        };
      });
      const groupByDate = groupBy(a, "date");
      return groupByDate;
    } else {
      return {};
    }
  }, [values]);

  useEffect(() => {
    if (nbDotDieuTriId) {
      getDataVitalSigns({
        nbDotDieuTriId: nbDotDieuTriId,
      });
      getToDieuTri({ nbDotDieuTriId, loai: 10 });
    }
  }, [nbDotDieuTriId]);

  const fomatData = (dsToDieuTri) => {
    let cloneData = cloneDeep(dsToDieuTri);
    let merged = [];
    let data = [];
    const isShowCdPhanBiet = (itemProps.listSapXepThongTinChung || [])?.find(
      (x) => x.fieldName === "toDieuTri.cdPhanBiet" && x.show
    );
    if (itemProps.tachToDieuTriKhiIn && !itemProps.gopToDieuTriNhieuNgay) {
      cloneData.sort(
        (a, b) => new Date(a.thoiGianYLenh) - new Date(b.thoiGianYLenh)
      );
      for (let i = 0; i < cloneData.length; i++) {
        const {
          tenKhoaChiDinh,
          tenPhong,
          soHieuGiuong,
          cdChinh,
          cdKemTheo,
          cdPhanBiet,
          ...rest
        } = cloneData[i];
        const keyCompare = {
          tenKhoaChiDinh,
          tenPhong,
          soHieuGiuong,
          cdChinh,
          cdKemTheo,
          ...(isShowCdPhanBiet ? { cdPhanBiet } : {}),
        };
        if (merged.length > 0) {
          const prev = merged[merged.length - 1];

          // so sánh với tờ liền kề
          const sameGroup = Object.keys(keyCompare).every(
            (k) => prev[k] === keyCompare[k]
          );

          if (sameGroup) {
            prev.dsToDieuTri.push(rest);
            continue;
          }
        }

        // nếu không giống liền kề thì tạo group mới
        merged.push({
          ...keyCompare,
          moTa: rest.moTa,
          cdSoBo: rest.cdSoBo,
          dsToDieuTri: [rest],
        });
      }
    } else {
      if (maBaoCao === "EMR_BA077.2") {
        cloneData = dsToDieuTri.map((item) => ({
          ...item,
          cdChinh: item.dsCdChinh?.length
            ? item.dsCdChinh.map((x) => x.ma + " - " + x.ten).join(",")
            : item.cdChinh,
          cdKemTheo: item.dsCdKemTheo?.length
            ? item.dsCdKemTheo.map((x) => x.ma + " - " + x.ten).join(",")
            : item.cdKemTheo,
        }));
      }
      merged = cloneData.reduce(
        (
          r,
          {
            tenKhoaChiDinh,
            tenPhong,
            soHieuGiuong,
            cdChinh,
            cdKemTheo,
            cdPhanBiet,
            ...rest
          }
        ) => {
          const key = [
            tenKhoaChiDinh,
            !itemProps.gopToDieuTriNhieuNgay && tenPhong,
            !itemProps.gopToDieuTriNhieuNgay && soHieuGiuong,
            !itemProps.gopToDieuTriNhieuNgay && cdChinh,
            !itemProps.gopToDieuTriNhieuNgay && cdKemTheo,
            isShowCdPhanBiet && cdPhanBiet,
          ]
            .filter(Boolean)
            .join("-");

          r[key] = r[key] || {
            tenKhoaChiDinh,
            ...(!itemProps.gopToDieuTriNhieuNgay
              ? {
                  tenPhong,
                  soHieuGiuong,
                  cdChinh,
                  cdKemTheo,
                  moTa: rest.moTa,
                  cdSoBo: rest.cdSoBo,
                }
              : {}),
            ...(isShowCdPhanBiet ? { cdPhanBiet } : {}),
            dsToDieuTri: [],
          };
          r[key]["dsToDieuTri"].push({
            ...rest,
            ...(itemProps.gopToDieuTriNhieuNgay
              ? { tenPhong, soHieuGiuong, cdChinh, cdKemTheo }
              : {}),
          });
          return r;
        },
        {}
      );
    }
    Object.keys(merged).forEach((item) => {
      let dataTime = [];
      const listData = cloneDeep(merged[item].dsToDieuTri).map((item2) => {
        const time = new Date(item2.thoiGianYLenh).getTime();
        dataTime.push(time);
        return {
          ...item2,
          time,
        };
      });
      const groupByTime = groupBy(listData, "time");
      let timeSort = {};
      Object.keys(groupByTime).forEach((time) => {
        if (groupByTime[time].length) {
          groupByTime[time].sort(
            (a, b) =>
              new Date(b.thoiGianYLenh).getTime() -
              new Date(a.thoiGianYLenh).getTime()
          );
          timeSort[time] = groupByTime[time];
        } else {
          timeSort[time] = groupByTime[time];
        }
      });
      dataTime = Array.from(new Set(dataTime));

      dataTime.sort((a, b) => a - b);
      const ngayDieuTris = dataTime.reduce((a, b) => {
        return [...a, ...timeSort[b]];
      }, []);
      let listIdCssAdded = [];
      ngayDieuTris.forEach((ngayDieuTri) => {
        if (!itemProps.hideDvTT) {
          ngayDieuTri.dsPtTt = (ngayDieuTri.dsPtTt || []).filter(
            (item) => item.maNhomDichVuCap1 !== MA_NHOM_DICH_VU_CAP1_TT
          );
        }
        const ngayYLenh = moment(ngayDieuTri.thoiGianYLenh).format(
          "DD/MM/YYYY"
        );
        const ngayThucHien = moment(ngayDieuTri.thoiGianThucHien).format(
          "DD/MM/YYYY"
        );

        if (ngoaiTru) {
          let chiSoSong = (dsChiSoSong[ngayThucHien] || []).filter(
            (item) =>
              item.chiDinhTuLoaiDichVu == LOAI_DICH_VU.KHAM &&
              item.chiDinhTuDichVuId == ngayDieuTri.id
          );
          chiSoSong = chiSoSong.sort(
            (a, b) =>
              new Date(b.thoiGianThucHien).getTime() -
              new Date(a.thoiGianThucHien).getTime()
          )[0];

          if (
            chiSoSong &&
            !listIdCssAdded.find((item) => item === chiSoSong.id)
          ) {
            ngayDieuTri.chiSoSong = chiSoSong;
            listIdCssAdded.push(chiSoSong.id);
          }
        } else {
          if (dsChiSoSong[ngayYLenh]) {
            let chiSoSong = (dsChiSoSong[ngayYLenh] || []).filter(
              (item) =>
                new Date(item.thoiGianThucHien).getTime() <=
                new Date(ngayDieuTri.thoiGianYLenh).getTime()
            );
            chiSoSong = chiSoSong.sort(
              (a, b) =>
                new Date(b.thoiGianThucHien).getTime() -
                new Date(a.thoiGianThucHien).getTime()
            )[0];

            if (
              chiSoSong &&
              !listIdCssAdded.find((item) => item === chiSoSong.id)
            ) {
              ngayDieuTri.chiSoSong = chiSoSong;
              listIdCssAdded.push(chiSoSong.id);
            }
          }
        }
      });

      merged[item].ngayDieuTris = ngayDieuTris;
      merged[item].thoiGianYLenh = ngayDieuTris[0].thoiGianYLenh;
      data.push(merged[item]);
    });
    data.sort(
      (a, b) =>
        new Date(a.thoiGianYLenh).getTime() -
        new Date(b.thoiGianYLenh).getTime()
    );
    return data;
  };

  useEffect(async () => {
    if (form && Object.keys(form).length) {
      if (Array.isArray(form.dsToDieuTri)) {
        const firstData = form.dsToDieuTri[0];
        let newData = form.dsToDieuTri.map((item) => {
          const listThuoc = (item?.dsThuoc || []).map((x) => ({
            ...x,
            value: x?.id,
            label: x?.tenDichVu,
          }));
          const listDvKt = (item?.dsDvKt || []).map((x) => ({
            ...x,
            value: x?.id,
            label: x?.tenDichVu,
          }));
          const lisdsCdhaTdcn = (item?.dsCdhaTdcn || []).map((x) => ({
            ...x,
            value: x?.id,
            label: x?.tenDichVu,
          }));
          return {
            ...item,
            dsDienBienBenh: (item.dsDienBienBenh || []).map((item1) => ({
              ...item1,
              listDvKt: ngoaiTru ? listDvKt : lisdsCdhaTdcn,
              listThuoc: listThuoc,
              dsDichVuKyThuatId: ngoaiTru ? item1.dsDvKtId : item1.dsCdhaTdcnId,
              dsDichVuThuocId: item1.dsThuocId,
            })),
          };
        });
        const _data = await Promise.all(
          newData.map(async (item, index) => {
            if (item.lichSuKy?.id) {
              const res = await getDuLieuTheoLichSuKyId({
                lichSuKyId: item.lichSuKy?.id,
              });
              const _toDieuTri = (res?.dsToDieuTri || []).find(
                (x) => x.id === item.id
              );
              if (_toDieuTri) {
                //lấy dữ liệu tại thời điểm ký + giữ lại lịch sử ký
                return {
                  ..._toDieuTri,
                  ...(maBaoCao === "EMR_BA077.2"
                    ? {
                        dsCdKemTheo: firstData?.dsCdKemTheo,
                        dsCdChinh: firstData?.dsCdChinh,
                      }
                    : {}),
                  lichSuKy: item.lichSuKy,
                  dsDienBienBenh: item.dsDienBienBenh,
                  moTaToDieuTri: item.moTaToDieuTri,
                  cdPhanBietToDieuTri: item.cdPhanBiet,
                };
              }
            }
            return item;
          })
        );

        setDsToDieuTriMemo(_data);
      }
    }
  }, [Object.keys(form)?.length, ngoaiTru, maBaoCao, dsChiSoSong]);

  const toDieuTris = useMemo(() => {
    if (mode === MODE.config) {
      return [
        {
          ngayDieuTris: [{}],
        },
      ];
    } else {
      if (dsToDieuTriMemo.length) {
        refValues.current = dsToDieuTriMemo;
        refId.current = [];
        return fomatData(dsToDieuTriMemo);
      }
    }
  }, [
    dsToDieuTriMemo,
    mode,
    dsChiSoSong,
    MA_NHOM_DICH_VU_CAP1_TT,
    itemProps.listSapXepThongTinChung,
    listToDieuTriByNb,
  ]);

  const onFormChange =
    (key, id, index = 0, toDieuTriId, isEditDienBien) =>
    (value) => {
      if (id) {
        const toDieuTri = refValues.current.find((item) => item.idGuid === id);
        if (toDieuTri && formChange["dsToDieuTri"]) {
          toDieuTri[key] = value;
          formChange["dsToDieuTri"](refValues.current);
        }
      } else {
        let toDieuTri = refValues.current?.find((x) => x.id === toDieuTriId);
        if (isEditDienBien) {
          const dienBienBenh = toDieuTri.dsDienBienBenh[index];
          dienBienBenh[key] = value;
          if (key === "dsDichVuKyThuatId") {
            if (ngoaiTru) {
              dienBienBenh.dsDvKtId = value;
            } else {
              dienBienBenh.dsCdhaTdcnId = value;
            }
          }
          if (key === "dsDichVuThuocId") {
            dienBienBenh.dsThuocId = value;
          }
        } else {
          if (!toDieuTri) {
            toDieuTri = refValues.current[index];
          }
          toDieuTri[key] = value;
        }

        if (formChange["dsToDieuTri"]) {
          formChange["dsToDieuTri"](refValues.current);
        }
      }
    };

  const onAddToDieuTri = (data) => async () => {
    const toDieuTri = data.ngayDieuTris[0];
    let res;
    if (ngoaiTru) {
      res = await nbDvKhamProvider.getById(id);
    }
    const dataToDieuTri = cloneDeep(dsToDieuTriMemo);
    const record = dataToDieuTri?.find((x) => x.id === toDieuTri.id);
    record.dsDienBienBenh = [
      ...record.dsDienBienBenh,
      {
        cdSoBo: toDieuTri?.cdSoBo,
        dsCdChinhId: toDieuTri?.dsCdChinhId,
        dsCdKemTheoId: toDieuTri?.dsCdKemTheoId,
        moTa: toDieuTri?.moTa,
        theBenhLao: toDieuTri?.theBenhLao,
        quaTrinhBenhLy: toDieuTri?.quaTrinhBenhLy,
        tienSuBanThan: toDieuTri?.tienSuBanThan,
        tienSuGiaDinh: toDieuTri?.tienSuGiaDinh,
        diUngThuoc: toDieuTri?.diUngThuoc,
        toanThan: toDieuTri?.toanThan,
        cacBoPhan: toDieuTri?.cacBoPhan,
        ghiChu: toDieuTri?.ghiChu,
        ...(ngoaiTru
          ? {
              chiSoSong: {
                ...res?.data?.nbChiSoSong,
                huyetAp: `${res?.data?.nbChiSoSong.huyetApTamThu || ""}${
                  res?.data?.nbChiSoSong.huyetApTamTruong
                    ? `/${res?.data?.nbChiSoSong.huyetApTamTruong}`
                    : ""
                }`,
                id: undefined,
              },
            }
          : { dienBienBenh: toDieuTri.dienBienBenh }),
        listThuoc: toDieuTri?.dsThuoc?.map((item) => ({
          ...item,
          value: item?.id,
          label: item?.tenDichVu,
        })),
        listDvKt: (toDieuTri?.dsDvKt || toDieuTri?.dsCdhaTdcn)?.map((item) => ({
          ...item,
          value: item?.id,
          label: item?.tenDichVu,
        })),
      },
    ];
    if (formChange["dsToDieuTri"]) {
      formChange["dsToDieuTri"](dataToDieuTri);
    }
    setDsToDieuTriMemo(dataToDieuTri);
  };

  const onDeleteToDieuTri = (index, parentId) => () => {
    const data = cloneDeep(refValues.current);
    let toDieuTri = data?.find((x) => x.id === parentId);
    const dsDienBienBenh = (toDieuTri?.dsDienBienBenh || [])?.filter(
      (x, idx) => idx !== index
    );
    toDieuTri.dsDienBienBenh = dsDienBienBenh;
    if (formChange["dsToDieuTri"]) {
      formChange["dsToDieuTri"](data);
    }
    setDsToDieuTriMemo(data);
  };

  const renderDSToDieuTri = useMemo(() => {
    return (toDieuTris || []).map((toDieuTri, index) => {
      return (
        <DanhSachToDieuTri
          onFormChange={onFormChange}
          key={index}
          mode={mode}
          toDieuTri={toDieuTri}
          stt={index + 1}
          toDieuTris={toDieuTris}
          itemProps={itemProps}
          form={form}
          // isDisable={!id}
          dsToDieuTri={form?.dsToDieuTri}
          isDisable={false}
          dsChiSoSong={dsChiSoSong}
          listCss={listCss}
          khoaPhieuSauKhiKy={itemProps.khoaPhieuSauKhiKy}
          showDuongDungThuoc={itemProps.showDuongDungThuoc}
          hienSoLuongDvt={itemProps.hienSoLuongDvt}
          hienThuocNhaThuoc={itemProps.hienThuocNhaThuoc}
          hienThuocKeNgoai={itemProps.hienThuocKeNgoai}
          hienThuocTuVan={itemProps.hienThuocTuVan}
          hienBSDieuTri={itemProps.hienBSDieuTri}
          hienThiGio={itemProps.hienThiGio}
          hienLoaiChiDinh={itemProps.hienLoaiChiDinh}
          hienThiSLBuoi={itemProps.hienThiSLBuoi}
          hienThiTocDoTruyen={itemProps.hienThiTocDoTruyen}
          hienThiDonViTocDoTruyen={itemProps.hienThiDonViTocDoTruyen}
          listSapXepYLenh={
            itemProps?.listSapXepYLenh?.length &&
            itemProps?.listSapXepYLenh?.length === LIST_SAP_XEP_Y_LENH?.length
              ? itemProps?.listSapXepYLenh
              : LIST_SAP_XEP_Y_LENH
          }
          listSapXepDienBienBenh={
            itemProps?.listSapXepDienBienBenh || LIST_SAP_XEP_DIEN_BIEN_BENH
          }
          gopDv={itemProps.gopDv}
          onAddToDieuTri={onAddToDieuTri}
          onDeleteToDieuTri={onDeleteToDieuTri}
          tuyChinhTruongSapXepYLenh={
            itemProps.tuyChinhTruongSapXepYLenh ||
            LIST_TUY_CHINH_TRUONG_SAP_XEP_Y_LENH
          }
          hienThiChanKyTruongKhoa={itemProps.hienThiChanKyTruongKhoa}
          hienThiTenTruongKhoa={itemProps.hienThiTenTruongKhoa}
          hienThiTenDayDu={itemProps.hienThiTenDayDu}
          gopToDieuTriNhieuNgay={itemProps.gopToDieuTriNhieuNgay}
          listToDieuTriByNb={listToDieuTriByNb}
          hienThiDvktNgungYLenhKhongThucHien={
            itemProps.hienThiDvktNgungYLenhKhongThucHien
          }
          hienThiDvKhoNgungYLenh={itemProps.hienThiDvKhoNgungYLenh}
          hienThiSoLuongYeuCauDvKho={itemProps.hienThiSoLuongYeuCauDvKho}
          hienThiTextTrangThaiHoanNgungYLenhKhongThucHienDvkt={
            itemProps.hienThiTextTrangThaiHoanNgungYLenhKhongThucHienDvkt
          }
          hienThiTextTrangThaiHoanNgungYLenhDvKho={
            itemProps.hienThiTextTrangThaiHoanNgungYLenhDvKho
          }
        />
      );
    });
  }, [toDieuTris]);

  return (
    <div data-type="to-dieu-tri">
      <GlobalStyle></GlobalStyle>
      {mode === MODE.config && (
        <>
          <div className="table-config">
            <Button
              icon={<SettingOutlined />}
              onClick={handleFocus}
              size={"small"}
            />
          </div>
        </>
      )}

      {renderDSToDieuTri}
    </div>
  );
});

ToDieuTri.defaultProps = {
  component: {},
  form: {},
};

ToDieuTri.propTypes = {
  component: T.shape({}),
  form: T.shape({}),
};

export default memo(ToDieuTri);
