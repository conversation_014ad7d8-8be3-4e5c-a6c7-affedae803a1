import goiSoProvider from "data-access/goi-so-provider";
import { message } from "antd";
import moment from "moment";
import cacheUtils from "lib-utils/cache-utils";
import { combineSort } from "utils";
import { isEqual } from "lodash";
import { t } from "i18next";
import { LIST_LOAI_QMS } from "constants/index";

export default {
  state: {
    readOnlyDsGoiNho: false,
    daThanhToan: true, // kiểm tra nb đã thanh toán nếu nhập mã nb
    messageChuaThanhToan: "", // nội dung thông báo chưa thanh toán
    dataSortColumn: { choKhamThuong: 1 },
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    tuChonQuayTiepDon: async (payload, state) => {
      if (state.goiSo.quayTiepDonId) return; //Nếu có quầy rồi thì bỏ qua
      let quayTiepDonId = await cacheUtils.read("COUNTERS_ID", "", null, false); //đọc thông tin quầy tiếp đón đã lưu trước đó
      const listAllQuayTiepDon = state.quayTiepDon.listAllQuayTiepDon;
      if (!quayTiepDonId) {
        //nếu không có thông tin
        if (listAllQuayTiepDon?.length) {
          const list = listAllQuayTiepDon.map((item) => item.id);
          quayTiepDonId = list[~~(Math.random() * list.length)]; // random quầy
        }
      }
      if (quayTiepDonId) {
        dispatch.goiSo.dongQuay({
          quayHienTai: quayTiepDonId,
          quayMoi: quayTiepDonId,
          khoaQuayTiepDonId: listAllQuayTiepDon?.find(
            (x) => x.id === quayTiepDonId
          )?.khoaId,
        });
        dispatch.goiSo.updateData({
          khoaQuayTiepDonId: listAllQuayTiepDon?.find(
            (x) => x.id === quayTiepDonId
          )?.khoaId,
        });
      }
    },
    setQuayTiepDon: async (quayTiepDonId = "", state) => {
      dispatch.goiSo.updateData({
        quayTiepDonId,
        readOnlyDsGoiNho: !quayTiepDonId,
      });
      cacheUtils.save("COUNTERS_ID", "", quayTiepDonId, false);
      return true;
    },
    searchGoiSo: (payload, state, ignoreCheckCardInsurance) => {
      return new Promise((resolve, reject) => {
        dispatch.tiepDon.updateData({ onSearchTime: new Date() }); //đánh dấu thời điểm search
        goiSoProvider
          .search(payload)
          .then((s) => {
            if (s?.code === 0 && s?.data?.length) {
              let data = s?.data?.length ? s.data[0] : {};
              const goiSoConfig = state.qms.goiSoConfig || {};
              if (data?.trangThai === 50) {
                message.error(t("qms.sttDaTiepDonKhongTheTiepDonLaiTrongNgay"));
                reject(s);
              } else {
                dispatch.goiSo.updateData({ dangTiepDonId: data?.id });
                dispatch.tiepDon.loadNguoiBenhLaySo(
                  data,
                  ignoreCheckCardInsurance
                );
                dispatch.goiSo.getNbTiepTheo({
                  id: state.goiSo.quayTiepDonId,
                  data: {
                    nbTiepTheoId: data.id, //sau khi search bệnh nhân tiếp đón theo stt thì gọi lệnh get người bệnh tiếp theo bỏ qua người bệnh hiện tại
                  },
                });
                if (goiSoConfig.SoLanDoc - (data.soLanGoi || 0) <= 0) {
                  dispatch.qms.soLanGoi({
                    id: data.id,
                    soLanGoi: 0,
                    loaiQms: LIST_LOAI_QMS.QMS_TIEP_DON,
                  });
                }
                resolve(data);
              }
            } else if (s?.data?.length === 0 && s?.code === 0) {
              message.error(t("qms.sttKhongTonTaiTrongNgay"));
              reject(s);
            } else {
              message.error(s?.message);
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    dongQuay: ({ quayHienTai, quayMoi = "", goiNguoiBenh = true }, state) => {
      return new Promise(async (resolve, reject) => {
        let id = state.tiepDon.nbLaySo?.id; //lấy id người bệnh lấy số hiện tại đang tiếp đón, để next bệnh nhân
        if (quayMoi) {
          id = undefined;
        }
        if (quayHienTai)
          goiSoProvider
            .dongQuay(quayHienTai)
            .then((s) => {
              dispatch.goiSo.setQuayTiepDon(quayMoi);
              dispatch.tiepDon.updateData({ stt: null, nbLaySo: null });
              if (quayMoi && goiNguoiBenh) {
                dispatch.goiSo.getNbTiepTheo({
                  id: quayMoi,
                  data: { nbTiepTheoId: id }, //khi đổi quầy thì bỏ qua nb đang tiếp đón
                  isLoadNguoiBenhTiepDon: true,
                });
              }
              resolve(s);
            })
            .catch((e) => {
              message.error(e?.message);
              reject(e);
            });
        else {
          dispatch.goiSo.setQuayTiepDon(quayMoi);
          dispatch.tiepDon.updateData({ stt: null, nbLaySo: null });
          if (quayMoi && goiNguoiBenh) {
            const data = await dispatch.goiSo.getNbTiepTheo({
              id: quayMoi,
              data: { nbTiepTheoId: id }, //khi đổi quầy thì bỏ qua nb đang tiếp đón
              isLoadNguoiBenhTiepDon: true,
            });
            resolve(data);
          } else {
            resolve(true);
          }
        }
      });
    },
    getListGoiNho: (payload) => {
      return new Promise((resolve, reject) => {
        goiSoProvider
          .getListGoiNho(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
            } else {
              reject(s);
              message.error(s?.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getListDaTiepDon: (payload) => {
      return new Promise((resolve, reject) => {
        goiSoProvider
          .getListDaTiepDon(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
            } else {
              reject(s);
              message.error(s?.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getListSlTheoPhong: (payload, state) => {
      const { dataSortColumn, ...rest } = payload;
      const sort = combineSort(
        dataSortColumn || state.goiSo.dataSortColumn || {}
      );
      return new Promise((resolve, reject) => {
        goiSoProvider
          .getListSlTheoPhong({ sort, ...rest })
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
            } else {
              reject(s);
              message.error(s?.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getNbTiepTheo: (payload = {}, state) => {
      const { id } = payload;
      if (!id) {
        return new Promise((resolve, reject) => {
          resolve();
          if (!payload.isGet && payload.goiNho) {
            message.error(t("tiepDon.vuiLongChonQuayTiepDon"));
          }
        });
      }
      return new Promise((resolve, reject) => {
        goiSoProvider
          .getNbTiepTheo(payload)
          .then((s) => {
            const nbDangTiepDon = s?.data?.dsDangTiepDon?.[0];
            const goiSoConfig = state.qms.goiSoConfig || {};
            if (payload.isLoadNguoiBenhTiepDon) {
              if (nbDangTiepDon) {
                dispatch.tiepDon.loadNguoiBenhLaySo(nbDangTiepDon, true);
                if (goiSoConfig.SoLanDoc - (nbDangTiepDon.soLanGoi || 0) <= 0) {
                  dispatch.qms.soLanGoi({
                    id: nbDangTiepDon.id,
                    soLanGoi: 0,
                    loaiQms: LIST_LOAI_QMS.QMS_TIEP_DON,
                  });
                }
              }
            }
            const nbTiepTheo = (s?.data?.dsChoTiepDon || [])[0] || null;
            if (!isEqual(state.goiSo.nbTiepTheo, nbTiepTheo)) {
              dispatch.goiSo.updateData({
                nbTiepTheo: nbTiepTheo, // mặc định dữ liệu bằng null nếu undefined để tránh useeffect check 2 lần
              });
            }
            resolve(s);
          })
          .catch((e) => {
            reject(e);
            dispatch.goiSo.updateData({ nbTiepTheo: null });
            dispatch.tiepDon.updateData({ nbLaySo: null, stt: null });
            if (e.code != "ERR_CANCELED")
              message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    huyTiepDon: (quayTiepDonId) => {
      return new Promise((resolve, reject) => {
        goiSoProvider
          .huyTiepDon(quayTiepDonId)
          .then((s) => {
            resolve(s);
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },

    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.goiSo.dataSortColumn,
        ...payload,
      };
      dispatch.goiSo.updateData({
        dataSortColumn,
      });
      dispatch.goiSo.getListSlTheoPhong({
        dataSortColumn,
      });
    },

    getInfoFromQr: ({ qrText, ...payload }, state) => {
      return new Promise(async (resolve, reject) => {
        try {
          const {
            data: { code, data },
          } = await goiSoProvider.getInfoFromQr({ qrText });
          if (code == 0) {
            const ngheNghiep = state.ngheNghiep.listAllNgheNghiep?.find(
              (item) => item.ma == data?.ngheNghiep?.ma
            );
            const quocTich = state.ttHanhChinh.listAllQuocGia?.find(
              (item) => item?.ma == data?.quocTich?.ma
            );
            const tinh = state.ttHanhChinh.listAllTinh?.find(
              (item) => item?.ma == data?.tinhThanhPho?.ma
            );
            const quanHuyen = state.ttHanhChinh.listAllQuanHuyen?.find(
              (item) => item?.ma == data?.quanHuyen?.ma
            );
            const xaPhuong = state.ttHanhChinh.listAllXaPhuong?.find(
              (item) => item?.ma == data?.xaPhuong?.ma
            );
            let newData = {
              ...data,
              ngheNghiepId: ngheNghiep?.id,
              ngheNghiep,
              quocTichId: quocTich?.id,
              quocTich,
              // id: data?.id,
              id: null,
              tenNb: data?.hoVaTen,
              gioiTinh: data?.gioiTinh,
              ngaySinh: data.ngaySinh && {
                str: data?.chiNamSinh
                  ? moment(data.ngaySinh).format("YYYY")
                  : moment(data.ngaySinh).format("DD/MM/YYYY"),
                date: moment(data.ngaySinh).format("YYYY-MM-DDTHH:mm:ssZ"),
              },
              ngheNghiepId: ngheNghiep?.id,
              ngheNghiep,
              thangTuoi: Math.ceil(
                moment
                  .duration(moment().diff(moment(data?.ngaySinh)))
                  .asMonths()
              ),
              tuoi: Math.ceil(
                moment.duration(moment().diff(moment(data?.ngaySinh))).asYears()
              ),
              soDienThoai: data.soDienThoai,
              nbNguoiBaoLanh: {
                ...data?.nguoiBaoHo,
                soDienThoai: data?.sdtNguoiBaoHo,
              },
              nbGiayToTuyThan: { loaiGiayTo: 2 },
              nbDiaChi: {
                soNha: data?.soNha,
                xaPhuong: { ...xaPhuong },
                xaPhuongId: xaPhuong?.id,
                quanHuyen: { ...quanHuyen },
                quanHuyenId: quanHuyen?.id,
                tinhThanhPho: { ...tinh },
                tinhThanhPhoId: tinh?.id,
                quocGia: { ...quocTich },
                quocGiaId: quocTich?.id,
                diaChi: `${data?.soNha}, ${data?.quanHuyen?.ten}, ${data?.tinhThanhPho?.ten}, ${quocTich?.ten}`,
              },
            };
            dispatch.tiepDon.updateData({ ...newData });
            resolve(data);
          } else reject(data);
        } catch (e) {
          reject(e);
        }
      });
    },

    kiemTraNbDaDenLuot: (payload) => {
      return new Promise((resolve, reject) => {
        goiSoProvider
          .kiemTraNbDaDenLuot(payload)
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
  }),
};
